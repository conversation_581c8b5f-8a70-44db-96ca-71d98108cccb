Imports System.Data.SqlClient
Imports System.IO
Imports System.Net
Imports System.Net.Sockets
Imports System.Text
Imports Parkrite
Imports Parkrite.ClientBackbone
Imports Parkrite.SystemShared
Imports Parkrite.SystemShared.Messaging
Imports Parkrite.SystemShared.NetworkReferences.DeviceServiceDefinition

Public Class TerminalManager

#Region "Class types"
    Private Structure QueuedTransaction
        Public RemovedTransType As TransactionTypeSelections
        Public TransToSend As TransactionMsg
    End Structure

    Private Structure RecordSeasonInfoFromSMC
        Public season As ClsSeasonInfo
        Public waitHand As Threading.ManualResetEvent
        Public theTimer As Threading.Timer
        Public HasRecord As Boolean
    End Structure

    Public Class EntityProcessing
        Public ticket As String
        Public timelabel As DateTime
        Public obj As Object
    End Class
#End Region

#Region "Private member variables"
    Private mDeviceBone As ParkriteClient
    Private mSysMode As SystemMode
    Private mTermType As DeviceServiceDefinition.DeviceType
    Private mTermInformation As Terminal
    Private mDBManager As DatabaseManager
    Private mHDwareMgr As TermHardwareManager
    Private mAntennaID As Byte
    Private mAntennaNo As Short
    Private mAddonOption As AddonOption
    Private mPGSEnabled As Boolean
    Private mPGS As PGSInfo
    Private mFullEpsIndex As Integer
    'Private mSemiEpsIndex As Integer
    Private mCashCardIndex As Integer
    'Private mREMIndex As Integer
    Private mFullSignBuffer As Integer = 0
    Private mFullSignOn As Boolean
    Private mIgnoreFullSign As Boolean
    Private mBypassFullSign As Boolean
    Private mManualFullSignOn As Boolean
    Private mPGSFullSignOn As Boolean
    Private mRelatedFromTerminal() As TerminalRelation
    Private mRelatedToTerminal() As TerminalRelation
    Private mIsFromTerminal As Boolean = False
    Private mIsToTerminal As Boolean = False
    Private mAllTerminals() As Terminal
    Private mTermsWithSameSubCP As New Hashtable
    Private mMainConfig As MainConfig
    Private mSelfTestedOK As Boolean
    Private mCCTVEnabled As Boolean
    Private mHouseKeepingTimer As Threading.Timer
    Private mComplimentaryHash As New Hashtable
    Private mSeasonGrpHash As New Hashtable
    Private mRunningSeasonRecords As New Hashtable
    Private mBlackListHash As New Hashtable
    Private mServices As ArrayList
    Private mAccessViolationHash As New Hashtable
    Private mEntitiesProcessing As New Hashtable
    Private mEntitiesProcessed As New Hashtable
    Private mRunningHourlyRecords As New Hashtable
    Private mTransactions As Queue
    Private mBypassFullSignTimer As Threading.Timer

    Private ReadOnly daytoexpir = "expirday.txt"

    Private mAntennaPort As String
    Private mAntennaType As String
    Private mLocalIPaddress As String
    Private mRemotePort As String
    Private mIsReset As Boolean
    Private mLprIP As String
    Private mLprUsername As String
    Private mLprPassword As String

    Private mTimerSeasonCheck As Threading.Timer 'Malbinda 2014-10-10

    Public CheckSeasonCarIUfromSMC As Boolean = False ' nay 2017-07-24 ~
    Public PartialSeasonDownload As Boolean = False
    Public CheckSeasonMotorCycleIUfromSMC As Boolean = False

    Private mWaitRecordQueryHashtable As New Hashtable
#End Region

    Public ForbidParkingDueToTimeOut As Boolean = True
    Public expday As Integer
    Public fullmsg As Boolean = False

#Region "Public events"
    Public Event VloopIsOn()
    Public Event VloopIsOff()
    Public Event NloopIsOff()   'add Nloop off 
    Public Event IUIsDetected(ByVal iu As String)
    Public Event MessageHandledByEPS(ByVal msg As TransactionMsg, ByVal fromIP As String)
#End Region
    Private TCPClient As TcpClient = New TcpClient()
#Region "Public Properties"
    Public ReadOnly Property TerminalType() As DeviceServiceDefinition.DeviceType
        Get
            Return mTermType
        End Get
    End Property

    Public ReadOnly Property DBManager() As DatabaseManager
        Get
            Return mDBManager
        End Get
    End Property

    Public ReadOnly Property HDManager() As TermHardwareManager
        Get
            Return mHDwareMgr
        End Get
    End Property

    Public ReadOnly Property TermID() As Integer
        Get
            Return mTermInformation.TerminalNo
        End Get
    End Property

    Public ReadOnly Property TermName() As String   'add on 25/06/08
        Get
            Return mTermInformation.TerminalName
        End Get
    End Property

    Public Property FullSignOn() As Boolean
        Get
            If mIgnoreFullSign = True Then
                Return False
            Else
                If mBypassFullSign = True Then
                    Return False
                Else
                    If mPGSEnabled = False Then
                        Return mFullSignOn Or mManualFullSignOn
                    Else
                        Return mFullSignOn Or mManualFullSignOn Or mPGSFullSignOn
                    End If
                End If
            End If
        End Get

        Set(ByVal value As Boolean) 'add to default full
            mFullSignOn = value
        End Set

    End Property

    Public ReadOnly Property IsTerminalInsideThisCarpark(ByVal termno As Short) As Boolean
        Get
            If mTermsWithSameSubCP.Contains(termno) = True Then
                Return True
            Else
                Return False
            End If
        End Get
    End Property

    Public ReadOnly Property AddOnOption() As AddonOption
        Get
            Return mAddonOption
        End Get
    End Property

    Public ReadOnly Property IsInnerCarpark() As Boolean
        Get
            If mTermInformation.SourceSubCarpark <> -1 And mTermInformation.DestinationSubCarpark <> -1 Then
                Return True
            Else
                Return False
            End If
        End Get
    End Property

    Public ReadOnly Property IsInnerCarpark(ByVal termno As Short) As Boolean
        Get
            For Each n As Terminal In mAllTerminals
                If n.TerminalNo = termno Then
                    If n.SourceSubCarpark <> -1 And n.DestinationSubCarpark <> -1 Then
                        Return True
                    Else
                        Return False
                    End If
                End If
            Next
            Return False
        End Get
    End Property

    Public ReadOnly Property IsFromTerminal() As Boolean
        Get
            Return mIsFromTerminal
        End Get
    End Property

    Public ReadOnly Property IsToTerminal() As Boolean
        Get
            Return mIsToTerminal
        End Get
    End Property

    Public ReadOnly Property IsRelatedFromTerminal(ByVal termno As Short) As Integer
        Get
            For i As Short = 0 To mRelatedFromTerminal.Length - 1
                If mRelatedFromTerminal(i).FromTerminal = termno Then
                    Return i
                End If
            Next
            Return -1
        End Get
    End Property

    Public ReadOnly Property IsRelatedToTerminal(ByVal termno As Short) As Integer
        Get
            For i As Short = 0 To mRelatedToTerminal.Length - 1
                If mRelatedToTerminal(i).ToTerminal = termno Then
                    Return i
                End If
            Next
            Return -1
        End Get
    End Property

    Public ReadOnly Property RelatedToTerminal(ByVal index As Short) As TerminalRelation
        Get
            Return mRelatedToTerminal(index)
        End Get
    End Property

    Public ReadOnly Property RelatedFromTerminal(ByVal index As Short) As TerminalRelation
        Get
            Return mRelatedFromTerminal(index)
        End Get
    End Property

    Public ReadOnly Property TermInfo() As Terminal
        Get
            Return mTermInformation
        End Get
    End Property


    Public Property FullSignBuffer() As Short
        Get
            Return mFullSignBuffer
        End Get

        Set(ByVal value As Short)
            mFullSignBuffer = value
        End Set
    End Property

    Public ReadOnly Property Eps() As ITerminalService
        Get
            If mFullEpsIndex = -1 Then
                Return Nothing
            Else
                Return mServices(mFullEpsIndex)
            End If
        End Get
    End Property

    Public ReadOnly Property SMCIPAddress() As String
        Get
            Return mDeviceBone.SMCIPAddress
        End Get
    End Property
#End Region

#Region "Constructor"
    Sub New(ByVal devBone As ParkriteClient,
            ByVal lip As String,
            ByVal antport As String,
            ByVal anttype As String,
            ByVal remote As String,
            ByVal lprIP As String,
             ByVal username As String,
              ByVal password As String)

        mLprIP = lprIP
        mLprUsername = username
        mLprPassword = password
        mAntennaPort = antport
        mAntennaType = anttype
        mLocalIPaddress = lip
        mRemotePort = remote
        'mLoopPairs = New Queue
        mTransactions = New Queue
        mSysMode = SystemMode.UNCONFIGURED
        mTermType = NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED
        mDeviceBone = devBone
        mDBManager = New DatabaseManager("DB" & mLocalIPaddress)
        mDBManager.CreateLocalDatabase()
        'mDBManager.DeleteAllRecords()
        AddHandler mDeviceBone.ServerSniffedEvent, AddressOf ServerSniffedEventHandler
        'AddHandler EpsService.EPSIUDetected, AddressOf HandleEPSIUDetected
        AddHandler mDeviceBone.ReceivedBulkEvent, AddressOf handleBulkEvent

        'mDetectedIUs = New ArrayList

        PartialSeasonDownload = My.Settings.PartialSeasonDownload
        Console.WriteLine("PartialSeasonDownload mode = " & PartialSeasonDownload.ToString)
        CheckSeasonCarIUfromSMC = My.Settings.CheckSeasonCarIUfromSMC
        Console.WriteLine("CheckSeasonCarIUfromSMC mode = " & CheckSeasonCarIUfromSMC.ToString)
        CheckSeasonMotorCycleIUfromSMC = My.Settings.CheckSeasonMotorCycleIUfromSMC
        Console.WriteLine("CheckSeasonMotorCycleIUfromSMC mode = " & CheckSeasonMotorCycleIUfromSMC.ToString)



        mFullEpsIndex = -1
        'mSemiEpsIndex = -1
        'mCashCardIndex = -1
        'mREMIndex = -1
        Dim path1 As String = System.IO.Path.GetDirectoryName(
                                              System.Reflection.Assembly.GetExecutingAssembly.GetName().CodeBase)
        daytoexpir = path1 & "\" & daytoexpir
        ReadExpDay()

    End Sub
#End Region

#Region "Public member functions"
    Public Function HasEps() As Boolean
        If mFullEpsIndex = -1 Then
            Return False
        Else
            Return True
        End If
    End Function

    Public Function HasCashCardSys() As Boolean
        If mCashCardIndex = -1 Then
            Return False
        Else
            Return True
        End If
    End Function

    Public Function RunningHourlyCount() As Integer
        SyncLock mRunningHourlyRecords.SyncRoot
            RunningHourlyCount = mRunningHourlyRecords.Count()
        End SyncLock
    End Function

    Public Function RunningSeasonCount() As Integer
        SyncLock mRunningSeasonRecords.SyncRoot
            RunningSeasonCount = mRunningSeasonRecords.Count()
        End SyncLock
    End Function

    'Public Function HasREMSys() As Boolean
    '    If mREMIndex = -1 Then
    '        Return False
    '    Else
    '        Return True
    '    End If
    'End Function

    Public Sub SpinUp()
        Try

            If SelfTest() = False Then
                Exit Sub
            Else
                If mSysMode = Enumeration.SystemMode.STANDBY Then
                    'SetupHoliday()
                    'SetupRate()
                    'SetupRateSet()
                    If mDBManager.GetMainConfigInfo(mTermInformation.CarparkNo, mMainConfig) <> True Then
                        'report error
                    End If

                    'Dim runn() As RunningHourly = mDBManager.RetrieveAllRunningHourlyEx()
                    'For Each run As RunningHourly In runn
                    '    UpdateRunning(run)
                    'Next

                    Dim seas() As RunningSeason = mDBManager.RetrieveAllRunningSeasonEx()
                    For Each sea As RunningSeason In seas
                        UpdateRunning(sea)
                    Next

                    Dim iuseason As DataTable = mDBManager.RetrieveAllIUSeasonInfo()
                    For Each rw As DataRow In iuseason.Rows
                        Eps.UpdateSeason(rw)
                    Next

                    Dim iuseasper As DataTable = mDBManager.RetrieveAllIUSeasonPermits
                    For Each rw As DataRow In iuseasper.Rows
                        Eps.UpdateSeasonPermits(rw)
                    Next

                    'Dim ccseason As DataTable = mDBManager.RetrieveAllCashCardSeasonInfo
                    'For Each rw As DataRow In ccseason.Rows
                    '    CashCardSys.UpdateSeason(rw)
                    'Next

                    'Dim ccseasper As DataTable = mDBManager.RetrieveAllCashCardSeasonPermits
                    'For Each rw As DataRow In ccseasper.Rows
                    '    CashCardSys.UpdateSeasonPermits(rw)
                    'Next

                    Dim seasgrp As DataTable = mDBManager.RetrieveAllSeasonGroup
                    For Each rw As DataRow In seasgrp.Rows
                        UpdateGroupSeasonInfo(rw)
                    Next

                    'Dim redmp As DataTable = mDBManager.RetrieveAllRedemption
                    'For Each rw As DataRow In redmp.Rows
                    '    UpdateRedemption(rw)
                    'Next

                    'Dim compli As DataTable = mDBManager.RetrieveAllComplimentary
                    'For Each rw As DataRow In compli.Rows
                    '    UpdateComplimentary(rw)
                    'Next

                    'Dim blklst As DataTable = mDBManager.RetrieveAllBlacklistedTicket
                    'For Each rw As DataRow In blklst.Rows
                    '    UpdateBlackListed(rw)
                    'Next

                    'mCCTVEnabled = mDBManager.CCTVIsEnabled
                    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.RequestCheckRunningSeasonCount, mDBManager.RetrieveAllRunningSeason.Rows.Count)
                    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.RequestCheckRunningHourlyCount, mDBManager.RetrieveAllRunningHourly.Rows.Count)
                    Threading.Thread.Sleep(2000)
                    mSysMode = Enumeration.SystemMode.ACTIVE
                    ' Console.WriteLine("............ ACTIVE MODE ............")
                End If
                mSelfTestedOK = True

            End If

            'mCCTVEnabled = True 'changed 25/07/08 permanent enabled


            'If mSysMode <> Enumeration.SystemMode.UNCONFIGURED Then
            '    mHDwareMgr.DisplayLCD("WELCOME", 1)
            'End If

            ''Read file to get ignoreFullsign flag
            'Dim filenm As String = IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly.GetName().CodeBase) & "\Config.txt"
            'If IO.File.Exists(filenm) = True Then
            '    Dim afile As New IO.FileInfo(filenm)
            '    Dim sr As IO.StreamReader = afile.OpenText
            '    Dim val As String = sr.ReadLine()
            '    sr.Close()
            '    If Trim(val).ToUpper = "TRUE" Then
            '        mIgnoreFullSign = True
            '    Else
            '        mIgnoreFullSign = False
            '    End If
            'Else
            '    mIgnoreFullSign = False
            'End If

            'Dim filenm1 As String = IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly.GetName().CodeBase) & "\Config1.txt"
            'If IO.File.Exists(filenm1) = True Then
            '    Dim afile As New IO.FileInfo(filenm1)
            '    Dim sr As IO.StreamReader = afile.OpenText
            '    Dim val As String
            '    val = sr.ReadLine()
            '    sr.Close()
            '    If Trim(val).ToUpper = "TRUE" Then
            '        mFullSignOn = True
            '        fullmsg = True
            '    Else
            '        mFullSignOn = False
            '        fullmsg = False
            '    End If
            'Else
            '    mFullSignOn = False
            '    fullmsg = False
            'End If

            'mCheckBarrierStatusTimer = New Threading.Timer(AddressOf CheckBarrierStatus, Nothing, 1000, 1000)
            'mHouseKeepingTimer = New Threading.Timer(AddressOf HouseKeepproc, Nothing, 6000, 6000)
            mHouseKeepingTimer = New Threading.Timer(AddressOf HouseKeepproc, Nothing, 15000, 15000) 'zaw changed to 15 sec


            ''This function shall run only for sentosa Integration is True, which webservice will pass Authoized IU here.
            If My.Settings.IsSentosaIntegration = True Then
                mTimerSeasonCheck = New Threading.Timer(AddressOf SeasonCheck, Nothing, 5000, 5000) 'Malbinda 2014-10-10 , it is for sentosa integration.
            End If

            'testtimer = New Threading.Timer(AddressOf testsend, Nothing, 250, 250)
            Dim startmsg As New TransactionMsg
            startmsg.TransactionType = ComCodes.TransactionTypeSelections.DeviceStartUp
            SendMsgToSMC(startmsg)

            If TerminalType = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device Then
                Dim tra As New TransactionMsg
                'tra.mLog = False
                tra.TransactionType = ComCodes.TransactionTypeSelections.RequestFullStatus
                SendMsgToSMC(tra)
            End If

        Catch ex As Exception
            Console.WriteLine(ex.ToString())
            TerminalSite.ErrorLog.Log("Error in TerminalManager SpinUp")
            TerminalSite.ErrorLog.Log(ex.ToString())
        End Try

    End Sub
    Private Sub SeasonCheck(ByVal obj As Object) 'Malbinda 2014-10-10, it is for sentosa season IU
        'Dim iuseason As DataTable = mDBManager.RetrieveAllIUSeasonInfo
        Dim iuseason As DataTable = mDBManager.RetrieveAllIUSeasonInfo_sentosa() ''zaw made changes, filter from query
        For Each rw As DataRow In iuseason.Rows
            'If IsDBNull(rw("SeasonType")) = False And IsDBNull(rw("Remark")) = False Then ''zaw disabled it
            '    If rw("SeasonType") = "999" And rw("Remark") = "AuthorizeIU" Then ' zaw disabled it
            Console.WriteLine("Season IU From WebService: " & rw(0))
            Eps.UpdateSeason(rw)
            'End If
            'End If
        Next
    End Sub

    Public Sub AddTranAndSend(ByVal ticket As String, ByVal trsmsg As TransactionMsg, ByVal Transtype As TransactionTypeSelections)
        SyncLock mEntitiesProcessing.SyncRoot
            If mEntitiesProcessing.Contains(ticket) Then
                mEntitiesProcessing.Remove(ticket)
            End If
        End SyncLock

        SyncLock mEntitiesProcessed.SyncRoot
            If mEntitiesProcessed.Contains(ticket) = False Then
                mEntitiesProcessed(ticket) = Now
            End If
        End SyncLock

        Console.WriteLine("AddTranAndSend - mTransactions.Count: " & mTransactions.Count)
        If mTransactions.Count = 0 Then
            trsmsg.mToIP = mDeviceBone.SMCIPAddress
            trsmsg.mLog = True
            trsmsg.mMulticast = True
            Dim QueTmp As New QueuedTransaction
            QueTmp.RemovedTransType = Transtype
            QueTmp.TransToSend = trsmsg
            mTransactions.Enqueue(QueTmp)
            Do
                Dim trans As QueuedTransaction = mTransactions.Dequeue
                If mTermType = DeviceType.Entry_Device Then
                    Console.WriteLine("AddTranAndSend Entry")
                    mDeviceBone.SendMsgOut(trans.TransToSend, False, MultiCastPort.Entry_Record1)
                Else
                    Console.WriteLine("AddTranAndSend Exit")
                    mDeviceBone.SendMsgOut(trans.TransToSend, False, MultiCastPort.Exit_Record1)
                End If
            Loop While mTransactions.Count > 0

        ElseIf mTransactions.Count > 0 Then
            Dim trans1 As QueuedTransaction = mTransactions.Peek()
            If CType(trans1.TransToSend.message1, String) = CType(trsmsg.message1, String) And (trans1.TransToSend.TransactionType = trsmsg.TransactionType) Then
                Dim msg As New TransactionMsg
                msg.mToIP = mDeviceBone.SMCIPAddress
                msg.mLog = False
                msg.message1 = trsmsg.message1
                msg.TransactionType = ComCodes.TransactionTypeSelections.DuplicateIULabel
                mDeviceBone.SendMsgOut(msg)
                Return
            End If
            Do
                Dim trans As QueuedTransaction = mTransactions.Dequeue
                If mTermType = DeviceType.Entry_Device Then
                    Console.WriteLine("AddTranAndSend Entry")
                    mDeviceBone.SendMsgOut(trans.TransToSend, False, MultiCastPort.Entry_Record1)
                Else
                    Console.WriteLine("AddTranAndSend Exit")
                    mDeviceBone.SendMsgOut(trans.TransToSend, False, MultiCastPort.Exit_Record1)
                End If
            Loop While mTransactions.Count > 0

            trsmsg.mToIP = mDeviceBone.SMCIPAddress
            trsmsg.mLog = True
            trsmsg.mMulticast = True
            Dim QueTmp As New QueuedTransaction
            QueTmp.RemovedTransType = Transtype
            QueTmp.TransToSend = trsmsg
            mTransactions.Enqueue(QueTmp)
        End If

        Console.WriteLine("Add the transaction....." & Me.mTermInformation.TerminalType.ToString)
    End Sub

    Public Sub AddTranAndSend_Orig(ByVal ticket As String, ByVal trsmsg As TransactionMsg, ByVal removedTranstype As TransactionTypeSelections)
        SyncLock mEntitiesProcessing.SyncRoot
            If mEntitiesProcessing.Contains(ticket) Then
                mEntitiesProcessing.Remove(ticket)
            End If
        End SyncLock

        SyncLock mEntitiesProcessed.SyncRoot
            If mEntitiesProcessed.Contains(ticket) = False Then
                mEntitiesProcessed(ticket) = Now
            End If
        End SyncLock


        If mTransactions.Count > 0 Then
            Dim trans1 As QueuedTransaction = mTransactions.Peek()
            If CType(trans1.TransToSend.message1, String) = CType(trsmsg.message1, String) And (trans1.TransToSend.TransactionType = trsmsg.TransactionType) Then
                Dim msg As New TransactionMsg
                msg.mToIP = mDeviceBone.SMCIPAddress
                msg.mLog = False
                msg.message1 = trsmsg.message1
                msg.TransactionType = ComCodes.TransactionTypeSelections.DuplicateIULabel
                mDeviceBone.SendMsgOut(msg)
                Return
            End If
            Do
                Dim trans As QueuedTransaction = mTransactions.Dequeue
                If mTermType = DeviceType.Entry_Device Then
                    Console.WriteLine("AddTranAndSend Entry")
                    mDeviceBone.SendMsgOut(trans.TransToSend, False, MultiCastPort.Entry_Record1)
                Else
                    'SyncLock mRecordNotFounds.SyncRoot
                    '    If mRecordNotFounds.Contains(trans.TransToSend.message1) = True Then
                    '        mRecordNotFounds.Remove(trans.TransToSend.message1)
                    '    End If
                    'End SyncLock
                    Console.WriteLine("AddTranAndSend Exit")
                    mDeviceBone.SendMsgOut(trans.TransToSend, False, MultiCastPort.Exit_Record1)
                End If
            Loop While mTransactions.Count > 0
        End If

        trsmsg.mToIP = mDeviceBone.SMCIPAddress
        trsmsg.mLog = True
        trsmsg.mMulticast = True
        Dim temp As New QueuedTransaction
        temp.RemovedTransType = removedTranstype
        temp.TransToSend = trsmsg
        mTransactions.Enqueue(temp)
        Console.WriteLine("Add the transaction....." & Me.mTermInformation.TerminalType.ToString)
    End Sub

    Public Sub SendAppendedTrans()
        If mTransactions.Count = 1 Then
            Dim trans As QueuedTransaction = mTransactions.Dequeue
            If mAddonOption.TailGateSensor = True Then
                'For i As Integer = 0 To mDetectedIUs.Count - 1
                '    If CType(mDetectedIUs(i), detectedIU).iu = trans.TransToSend.message1 Then
                '        Dim tmp As detectedIU = CType(mDetectedIUs(i), detectedIU)
                '        tmp.sentUp = True
                '        mDetectedIUs(i) = tmp
                '    End If
                'Next
            End If
            If mTermType = DeviceType.Entry_Device Then
                mDeviceBone.SendMsgOut(trans.TransToSend, False, MultiCastPort.Entry_Record1)
            Else
                'SyncLock mRecordNotFounds.SyncRoot
                '    If mRecordNotFounds.Contains(trans.TransToSend.message1) = True Then
                '        mRecordNotFounds.Remove(trans.TransToSend.message1)
                '    End If
                'End SyncLock
                mDeviceBone.SendMsgOut(trans.TransToSend, False, MultiCastPort.Exit_Record1)
            End If

        ElseIf mTransactions.Count = 0 Then
            'If (mDetectedIUs.Count <> 0) And (mLastOpenbarrierIsSuspending = False) And mAddonOption.TailGateSensor = True Then
            '    Dim thedet As detectedIU = CType(mDetectedIUs(mDetectedIUs.Count - 1), detectedIU)
            '    Dim iu As String = String.Empty
            '    If thedet.sentUp = False Then
            '        iu = thedet.iu
            '    End If
            '    Dim trans As New TransactionMsg
            '    trans.TransactionType = ComCodes.TransactionTypeSelections.ReportTailGating
            '    If iu <> String.Empty Then
            '        trans.message1 = iu
            '    End If
            '    trans.mLog = False
            '    'mTailgateReports.Enqueue(trans)
            '    SendMsgToSMC(trans)
            'End If
        End If
    End Sub

    Public Sub UpdateRunning(ByVal running As Object)
        If running.GetType.Name = "RunningHourly" Then
            Dim runn As RunningHourly = CType(running, RunningHourly)
            SyncLock mRunningHourlyRecords.SyncRoot
                If mRunningHourlyRecords.Contains(runn.HourlyNo) Then
                    mRunningHourlyRecords.Remove(runn.HourlyNo)
                End If
                mRunningHourlyRecords(runn.HourlyNo) = running
            End SyncLock
        Else
            Dim runn As RunningSeason = CType(running, RunningSeason)
            SyncLock mRunningSeasonRecords.SyncRoot
                If mRunningSeasonRecords.Contains(runn.SeasonNo) Then
                    mRunningSeasonRecords.Remove(runn.SeasonNo)
                End If
                mRunningSeasonRecords(runn.SeasonNo) = running
            End SyncLock
        End If

    End Sub

    Public Sub RemoveRunning(ByVal running As Object)
        If running.GetType.Name = "RunningHourly" Then
            Dim runn As RunningHourly = CType(running, RunningHourly)
            SyncLock mRunningHourlyRecords.SyncRoot
                If mRunningHourlyRecords.Contains(runn.HourlyNo) Then
                    mRunningHourlyRecords.Remove(runn.HourlyNo)
                End If
            End SyncLock
        Else
            Dim runn As RunningSeason = CType(running, RunningSeason)
            SyncLock mRunningSeasonRecords.SyncRoot
                If mRunningSeasonRecords.Contains(runn.SeasonNo) Then
                    mRunningSeasonRecords.Remove(runn.SeasonNo)
                End If
            End SyncLock
        End If
    End Sub

    Public Sub RemoveRunning(ByVal ticket As String, ByVal ishourly As Boolean)
        If ishourly = True Then
            SyncLock mRunningHourlyRecords.SyncRoot
                If mRunningHourlyRecords.Contains(ticket) Then
                    mRunningHourlyRecords.Remove(ticket)
                End If
            End SyncLock
        Else
            SyncLock mRunningSeasonRecords.SyncRoot
                If mRunningSeasonRecords.Contains(ticket) Then
                    mRunningSeasonRecords.Remove(ticket)
                End If
            End SyncLock
        End If
    End Sub
    Public Function GetRunningHourlyRecord(ByVal ticket As String, ByRef ret As RunningHourly) As Boolean
        ret = Nothing
        Return False
    End Function

    Public Function GetRunningSeasonRecord(ByVal ticket As String, ByRef ret As RunningSeason) As Boolean
        ret = Nothing
        SyncLock mRunningSeasonRecords.SyncRoot
            If mRunningSeasonRecords.Contains(ticket) = True Then
                ret = mRunningSeasonRecords(ticket)
                GetRunningSeasonRecord = True
            Else
                GetRunningSeasonRecord = False
            End If
        End SyncLock
    End Function

    Public Sub UpdateGroupSeasonInfo(ByVal rw As DataRow)
        Dim tmpSeasgrp As New ClsSeasonGroup
        tmpSeasgrp.GroupNo = rw(0)
        tmpSeasgrp.Maximum = rw(2)
        tmpSeasgrp.Threshold = rw(3)
        tmpSeasgrp.CurrentCount = rw(4)
        SyncLock mSeasonGrpHash.SyncRoot
            If mSeasonGrpHash.Contains(tmpSeasgrp.GroupNo) Then
                mSeasonGrpHash.Remove(tmpSeasgrp.GroupNo)
            End If
            mSeasonGrpHash(tmpSeasgrp.GroupNo) = tmpSeasgrp
        End SyncLock
    End Sub

    Public Sub UpdateGroupSeasonInfo(ByVal grp As ClsSeasonGroup)
        SyncLock mSeasonGrpHash.SyncRoot
            If mSeasonGrpHash.Contains(grp.GroupNo) Then
                mSeasonGrpHash.Remove(grp.GroupNo)
            End If
            mSeasonGrpHash(grp.GroupNo) = grp
        End SyncLock
    End Sub

    Public Function GetGroupSeasonInfo(ByVal grpno As Integer, ByRef ret As ClsSeasonGroup) As Boolean
        ret = Nothing
        SyncLock mSeasonGrpHash.SyncRoot
            If mSeasonGrpHash.Contains(grpno) = True Then
                ret = mSeasonGrpHash(grpno)
                GetGroupSeasonInfo = True
            Else
                GetGroupSeasonInfo = False
            End If
        End SyncLock
    End Function

    Public Sub ClearGroupSeasonInfo()
        SyncLock mSeasonGrpHash.SyncRoot
            mSeasonGrpHash.Clear()
        End SyncLock
    End Sub

    Public Overloads Sub UpdateGroupSeasonPermits(ByVal rw As Parkrite.SystemShared.GlobalVariables.GroupSeasonPermits)
        SyncLock mSeasonGrpHash.SyncRoot
            If mSeasonGrpHash.Contains(rw.GroupNo) Then
                Dim aa As ClsSeasonGroup = mSeasonGrpHash(rw.GroupNo)
                If aa.Accesses.Contains(rw.SubCarparkNo) = False Then
                    aa.Accesses.Add(rw.SubCarparkNo)
                End If
            End If
        End SyncLock
    End Sub

    Public Overloads Sub UpdateGroupSeasonPermits(ByVal rw As System.Data.DataRow)
        SyncLock mSeasonGrpHash.SyncRoot
            If mSeasonGrpHash.Contains(rw(1)) Then
                Dim aa As ClsSeasonGroup = mSeasonGrpHash(rw(1))
                If aa.Accesses.Contains(rw(3)) = False Then
                    aa.Accesses.Add(rw(3))
                End If
            End If
        End SyncLock
    End Sub

    Public Sub UpdateBlackListed(ByVal blk As BlacklistedTicket)
        SyncLock mBlackListHash.SyncRoot
            If mBlackListHash.Contains(blk.TicketNo) = True Then
                mBlackListHash.Remove(blk.TicketNo)
            Else
                mBlackListHash(blk.TicketNo) = blk
            End If
        End SyncLock
    End Sub

    Public Sub DeleteGroupSeasonPermits(ByVal Grpno As Integer, ByVal subcpno As Short)
        SyncLock mSeasonGrpHash.SyncRoot
            If mSeasonGrpHash.Contains(Grpno) = True Then
                Dim aa As ClsSeasonGroup = mSeasonGrpHash(Grpno)
                If aa.Accesses.Contains(subcpno) = True Then
                    aa.Accesses.Remove(subcpno)
                End If
            End If
        End SyncLock
    End Sub

    Public Sub RemoveBlackListed(ByVal blkno As String)
        SyncLock mBlackListHash.SyncRoot
            If mBlackListHash.Contains(blkno) Then
                mBlackListHash.Remove(blkno)
            End If
        End SyncLock
    End Sub

    Public Sub UpdateAccessViolation(ByVal acc As AccessViolation)
        SyncLock mAccessViolationHash.SyncRoot
            If mAccessViolationHash.Contains(acc.TicketNo) Then
                mAccessViolationHash.Remove(acc.TicketNo)
            End If
            mAccessViolationHash(acc.TicketNo) = acc
        End SyncLock
    End Sub

    Public Sub RemoveAccessViolation(ByVal tkno As String)
        SyncLock mAccessViolationHash.SyncRoot
            If mAccessViolationHash.Contains(tkno) Then
                mAccessViolationHash.Remove(tkno)
            End If
        End SyncLock
    End Sub

    Public Function GetAccessViolation(ByVal ticket As String, ByRef ret As AccessViolation) As Boolean
        ret = Nothing
        SyncLock mAccessViolationHash.SyncRoot
            If mAccessViolationHash.Contains(ticket) = True Then
                ret = mAccessViolationHash(ticket)
                GetAccessViolation = True
            Else
                GetAccessViolation = False
            End If
        End SyncLock
    End Function

    Public Sub RemoveGroupSeasonInfo(ByVal grpno As Integer)
        SyncLock mSeasonGrpHash.SyncRoot
            If mSeasonGrpHash.Contains(grpno) Then
                mSeasonGrpHash.Remove(grpno)
            End If
        End SyncLock
    End Sub

    Public Function IsInProcessing(ByVal str As String) As Boolean
        SyncLock mEntitiesProcessing.SyncRoot
            IsInProcessing = mEntitiesProcessing.Contains(str)
        End SyncLock
    End Function

    Public Function IsProcessed(ByVal str As String) As Boolean
        SyncLock mEntitiesProcessed.SyncRoot
            IsProcessed = mEntitiesProcessed.Contains(str)
        End SyncLock
    End Function

    Public Sub AddToInProcessing(ByVal str As String, Optional ByVal obj As Object = Nothing)
        SyncLock mEntitiesProcessing.SyncRoot
            If mEntitiesProcessing.Contains(str) = False Then
                Dim a As New EntityProcessing
                a.ticket = str
                a.timelabel = Now
                If IsNothing(obj) = False Then
                    a.obj = obj
                End If
                mEntitiesProcessing(str) = a
                'Console.WriteLine("Information added to Processing " & str)
            End If
        End SyncLock
    End Sub

    Public Sub UpdateInProcessing(ByVal str As String, Optional ByVal obj As Object = Nothing)
        SyncLock mEntitiesProcessing.SyncRoot
            If mEntitiesProcessing.Contains(str) = True Then
                Dim a As New EntityProcessing
                a.ticket = str
                a.timelabel = Now
                If IsNothing(obj) = False Then
                    a.obj = obj
                End If
                mEntitiesProcessing(str) = a
                'Console.WriteLine("Information added to Processing " & str)
            End If
        End SyncLock
    End Sub

    Public Sub RemoveFromProcessing(ByVal str As String)
        SyncLock mEntitiesProcessing.SyncRoot
            If mEntitiesProcessing.Contains(str) = True Then
                mEntitiesProcessing.Remove(str)
                'Console.WriteLine("Information Removed from Processing " & str)
            End If
        End SyncLock
    End Sub

    Public Sub RemoveFromProcessed(ByVal str As String)
        If mTermInformation.TerminalType = TERMINALOPTION.ENTRYTERM Then
            Console.WriteLine(True)
        End If
        SyncLock mEntitiesProcessed.SyncRoot
            If mEntitiesProcessed.Contains(str) = True Then
                mEntitiesProcessed.Remove(str)
            End If
        End SyncLock
    End Sub

    Public Function GetObjFromProcessing(ByVal str As String) As EntityProcessing
        SyncLock mEntitiesProcessing.SyncRoot
            If mEntitiesProcessing.Contains(str) = True Then
                GetObjFromProcessing = mEntitiesProcessing(str)
            Else
                GetObjFromProcessing = Nothing
            End If
        End SyncLock
    End Function

    Public Function GetObjFromProcessed(ByVal str As String) As Object
        SyncLock mEntitiesProcessed.SyncRoot
            If mEntitiesProcessed.Contains(str) = True Then
                GetObjFromProcessed = mEntitiesProcessed(str)
            Else
                GetObjFromProcessed = Nothing
            End If
        End SyncLock
    End Function

    Public Function IsBlacklisted(ByVal ticket As String, ByRef theinfo As BlacklistedTicket) As Boolean
        SyncLock mBlackListHash.SyncRoot
            If mBlackListHash.Contains(ticket) = True Then
                IsBlacklisted = True
                theinfo = mBlackListHash(ticket)
            Else
                IsBlacklisted = False
            End If
        End SyncLock
    End Function

    Public Sub SendMsgToSMC(ByVal msg As TransactionMsg, Optional ByVal high As Boolean = False)
        msg.mToIP = mDeviceBone.SMCIPAddress
        mDeviceBone.SendMsgOut(msg, high)
    End Sub

    Public Sub SendSimpleCmdToServer(ByVal typ As TransactionTypeSelections, ByVal devtype As DeviceType, ByVal ip As String, Optional ByVal log As Boolean = False)
        Dim msg As New TransactionMsg
        msg.mToIP = ip
        msg.TransactionType = typ
        msg.mLog = log
        msg.mDestdevice = devtype
        msg.message1 = mTermInformation.TerminalNo
        mDeviceBone.SendMsgOut(msg)
    End Sub

    Public Sub SendSimpleErrorToSMC(ByVal cardno As String, ByVal transtype As TransactionTypeSelections)
        Dim msg As New TransactionMsg
        msg.mToIP = mDeviceBone.SMCIPAddress
        msg.TransactionType = transtype
        msg.mLog = False
        msg.mDestdevice = DeviceType.CMC_Device
        msg.message1 = cardno
        msg.message2 = mTermInformation.TerminalNo
        msg.message3 = Now.ToString("dd/MM/yyyy HH:mm:ss")
        mDeviceBone.SendMsgOut(msg, True)
    End Sub

    Public Sub replyDownloadMsgError(ByVal ty As TransactionTypeSelections, ByVal id As Object, ByVal str As String)
        Dim msg As New TransactionMsg
        msg.mToIP = mDeviceBone.SMCIPAddress
        msg.TransactionType = ComCodes.TransactionTypeSelections.ReplyDowloadError
        msg.message1 = id
        msg.message2 = str
        msg.mLog = False
        msg.mDestdevice = NetworkReferences.DeviceServiceDefinition.DeviceType.CMC_Device
        mDeviceBone.SendMsgOut(msg)
    End Sub

    Public Sub ReplyAllRecordsCount(ByVal ty As TransactionTypeSelections, ByVal cnt As Integer, Optional ByVal log As Boolean = False)
        Dim msg As New TransactionMsg
        msg.mToIP = mDeviceBone.SMCIPAddress
        msg.TransactionType = ty
        msg.message1 = cnt
        msg.mLog = log
        msg.mDestdevice = NetworkReferences.DeviceServiceDefinition.DeviceType.CMC_Device
        mDeviceBone.SendMsgOut(msg)
    End Sub

    Public Sub ProcessResponseFromSMC(ByVal msg As TransactionMsg)
        'Dim key As String = CType(msg.message1, String) & ";" & CType(msg.message2, String)
        'If mWaitRecordQueryHashtable.Contains(key) Then
        '    Dim theinfo As RecordInfoFromSMC = mWaitRecordQueryHashtable(key)
        '    If Not theinfo.theTimer Is Nothing Then
        '        theinfo.theTimer.Dispose()
        '    End If
        '    If CType(msg.message3, String) = "TRUE" Then
        '        theinfo.HasRecord = True
        '        theinfo.hourly.EntryTime = DateTime.ParseExact(CType(msg.message4, String), "dd/MM/yyyy HH:mm:ss", Nothing)
        '        Dim strstrstr() As String = CType(msg.message5, String).Split(";"c)
        '        theinfo.hourly.HourlyType = CType(strstrstr(1), Integer)
        '        theinfo.hourly.TerminalNo = strstrstr(0)
        '        theinfo.hourly.Status = msg.message6
        '    Else
        '        theinfo.HasRecord = False
        '    End If
        '    mWaitRecordQueryHashtable(key) = theinfo
        '    theinfo.waitHand.Set()
        'End If
    End Sub

    ''Check SeasonIU again to SMC if not available in terminal side.
    Public Function ProcessRequestSeasonIUFromSMC(ByVal ticketno As String, ByVal typ1 As ComCodes.TransactionTypeSelections, ByVal typ As ComCodes.TransactionTypeSelections, ByRef clsseason As ClsSeasonInfo) As Boolean
        Try
            'Console.WriteLine("Requesting Season IU from SMC ...")
            Dim thekey As String = ticketno & ";" & CType(typ1, Integer)
            Dim msgs As New TransactionMsg
            msgs.TransactionType = typ
            msgs.message1 = ticketno
            msgs.message2 = mTermInformation.TerminalNo
            msgs.message3 = CType(typ1, Integer)


            ''Added to check access permit.
            If TermInfo.TerminalType = Enumeration.TERMINALOPTION.EXITTERM Then
                msgs.message4 = mTermInformation.CarparkNo & ";" & mTermInformation.SourceSubCarpark
            Else
                msgs.message4 = mTermInformation.CarparkNo & ";" & mTermInformation.DestinationSubCarpark
            End If



            msgs.mMulticast = False
            msgs.mLog = False

            Dim thehand As New Threading.ManualResetEvent(False)
            Dim thetimer As New Threading.Timer(AddressOf ProcessResponseOfRetrieveSeasonIU, thekey, 3000, 3000)

            Dim theinfo As RecordSeasonInfoFromSMC
            theinfo.season = New ClsSeasonInfo
            theinfo.theTimer = thetimer
            theinfo.waitHand = thehand
            mWaitRecordQueryHashtable(thekey) = theinfo

            SendMsgToSMC(msgs)
            Console.WriteLine("ProcessRequestSeasonIUFromSMC for " & ticketno)
            TerminalSite.TerminalLog.Log("ProcessRequestSeasonIUFromSMC for " & ticketno)
            thehand.WaitOne()

            theinfo = mWaitRecordQueryHashtable(thekey)
            mWaitRecordQueryHashtable.Remove(thekey)
            If theinfo.HasRecord = True Then
                'HDManager.DisplayLED("Processing ...", "")
                'HDManager.DisplayLCD("Processing ...")
                'Console.WriteLine("ProcessVerifiedSeasonIUfromSMC Has Record ...>>>")
                'Console.WriteLine("Season Open Barrier ...>>>" & theinfo.hourly.Status)
                'Console.WriteLine("Season Open Barrier ...>>>" & theinfo.hourly.HourlyNo)

                clsseason = theinfo.season
                Return True

            Else
                Return False
            End If

        Catch ex As Exception
            Console.WriteLine("ProcessVerifiedSeasonIUfromSMC " & ex.ToString)
            TerminalSite.ErrorLog.Log("ProcessVerifiedSeasonIUfromSMC " & ex.ToString)
            Return False
        End Try
    End Function

    Public Sub ProcessResponseSeasonIUFromSMC(ByVal msg As TransactionMsg)

        'Console.WriteLine("ProcessResponseSeasonIUFromSMC >>>")
        Dim key As String = CType(msg.message1, String) & ";" & CType(msg.message2, String)
        'Console.WriteLine("Key >>>" & key)

        If mWaitRecordQueryHashtable.Contains(key) Then
            Dim theinfo As RecordSeasonInfoFromSMC = mWaitRecordQueryHashtable(key)

            If Not theinfo.theTimer Is Nothing Then
                theinfo.theTimer.Dispose()
            End If

            If CType(msg.message3, String) = "TRUE" Then
                Try
                    'Console.WriteLine("***** ProcessResponseSeasonIUFromSMC TRUE >>>")
                    theinfo.HasRecord = True

                    theinfo.season.Ticket = msg.message1

                    Dim strr() As String = CType(msg.message5, String).Split(";"c)

                    theinfo.season.SeasonType = CType(strr(0), Short)
                    theinfo.season.GroupNo = CType(strr(1), Short)
                    theinfo.season.Freeze = CType(strr(2), Boolean)
                    theinfo.season.IOCheck = CType(strr(3), Boolean)
                    theinfo.season.ValidFrom = DateTime.ParseExact(CType(strr(4), String), "dd/MM/yyyy HH:mm:ss", Nothing)
                    theinfo.season.ValidTo = DateTime.ParseExact(CType(strr(5), String), "dd/MM/yyyy HH:mm:ss", Nothing)

                Catch ex As Exception
                    Console.WriteLine("ProcessResponseSeasonIUFromSMC: " & ex.ToString())
                    TerminalSite.ErrorLog.Log("ProcessResponseSeasonIUFromSMC: " & ex.ToString())
                End Try
                'ProcessRecvedEntryTransaction(

            Else
                theinfo.HasRecord = False
                'Console.WriteLine("ProcessResponseSeasonIUFromSMC FALSE >>>")
            End If


            mWaitRecordQueryHashtable(key) = theinfo
            Console.WriteLine("ProcessResponseSeasonIUFromSMC for " & key)
            TerminalSite.TerminalLog.Log("ProcessResponseSeasonIUFromSMC for " & key)
            theinfo.waitHand.Set()
        End If
    End Sub

    Private Sub ProcessResponseOfRetrieveSeasonIU(ByVal obj As Object)
        'Console.WriteLine("ProcessResponseOfRetrieveSeasonIU >>> ")
        Try
            'Console.WriteLine("ProcessResponseOfRetrieveSeasonIU >>> 1")
            Dim thekey As String = CType(obj, String)
            'Console.WriteLine("ProcessResponseOfRetrieveSeasonIU >>> 2 " & thekey)
            If mWaitRecordQueryHashtable.Contains(thekey) Then
                'Console.WriteLine("ProcessResponseOfRetrieveSeasonIU >>> 3")
                Dim theinfo As RecordSeasonInfoFromSMC = mWaitRecordQueryHashtable(thekey)

                If Not theinfo.theTimer Is Nothing Then
                    theinfo.theTimer.Dispose()
                End If

                'Console.WriteLine("ProcessResponseOfRetrieveSeasonIU >>> 4")
                Dim strs() As String = CType(obj, String).Split(";"c)

                HDManager.DisplayLED("Processing..", "")
                HDManager.DisplayLCD("Processing ...")

                SendSimpleErrorToSMC(strs(0), CType(strs(1), Integer))
                Console.WriteLine("ProcessResponseOfRetrieveSeasonIU for " & thekey)
                TerminalSite.TerminalLog.Log("ProcessResponseOfRetrieveSeasonIU for " & thekey)
                theinfo.waitHand.Set()

                'Console.WriteLine("ProcessResponseOfRetrieveSeasonIU Key>>> " & thekey)
                ''''''mWaitRecordQueryHashtable.Remove(thekey)
                'Console.WriteLine("ProcessResponseOfRetrieveSeasonIU >>> " & strs(0) & "," & strs(1))
            End If
        Catch ex As Exception
            Console.WriteLine("ProcressResponseOfRetrieveSeasonIU: " & ex.ToString)
            TerminalSite.ErrorLog.Log("ProcressResponseOfRetrieveSeasonIU: " & ex.ToString)
        End Try
    End Sub
#End Region

#Region "Private member funtions"

    Private Function SelfTest() As Boolean
        If mDBManager.GetTerminalInfo(mLocalIPaddress, mTermInformation) = True Then
            mSysMode = Enumeration.SystemMode.STANDBY
            Select Case mTermInformation.TerminalType
                Case Enumeration.TERMINALOPTION.ENTRYTERM
                    mTermType = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device
                    mDeviceBone.IgnoreHPC = True
                    'mDeviceBone.DeviceNature.MyDeviceType = DeviceType.Entry_Device
                Case Enumeration.TERMINALOPTION.EXITTERM
                    mTermType = NetworkReferences.DeviceServiceDefinition.DeviceType.Exit_Device
                    'mDeviceBone.DeviceNature.MyDeviceType = DeviceType.Entry_Device
                Case Else
                    Throw New ApplicationException("Wrong Terminal type")
            End Select
            Dim workmds() As WorkingMode
            Dim subcbn As Short = IIf(mTermInformation.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM, mTermInformation.DestinationSubCarpark, mTermInformation.SourceSubCarpark)
            If mDBManager.GetWorkingModeForTerminal(mTermInformation.CarparkNo, subcbn, workmds) = True Then
                GenerateServices(workmds)
            End If
        End If
        Dim testdbr As SelfTestResult = mDBManager.SelfTest(HasEps, HasCashCardSys, False)
        If testdbr = SelfTestResult.FINE Then
            mAntennaID = mDBManager.FindAntennaID(mTermInformation.TerminalNo)
            mAntennaNo = mDBManager.FindAntennaNo(mTermInformation.TerminalNo)
            initHardwareManager()
            If mDBManager.GetAddonOptionInfo(mAddonOption) = False Then
                Throw New ApplicationException("Cannot get Addon option information")
            Else
                If mTermInformation.SourceSubCarpark = -1 Or mTermInformation.DestinationSubCarpark = -1 Then
                    mHDwareMgr.LEDEnabled = mAddonOption.LEDEnabled
                ElseIf mTermInformation.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM Then
                    mHDwareMgr.LEDEnabled = mAddonOption.LEDEnabled
                Else
                    mHDwareMgr.LEDEnabled = False
                End If

                mHDwareMgr.ShutterEnabled = mAddonOption.ShutterEnabled
            End If
            mPGSEnabled = mDBManager.PGSIsEnabled()
            If mPGSEnabled = True Then
                If mDBManager.GetPGSInfo(mTermInformation.TerminalNo, mPGS) = False Then
                    mPGS.TerminalNo = mTermInformation.TerminalNo
                    mPGS.Connected = False
                    mPGS.InformedBy = -1
                End If
            End If

            'CType(Eps, EpsService).HasCHU = False

            'If mHDwareMgr.SelfTest() = SelfTestResult.FATALREMERROR Then
            '    'stop the system
            '    Return False
            'End If

            'mHDwareMgr.DetectInterval = My.Settings.DetectInterval


            'jswei
            ' Connect to TCP server
            'Try
            '    TcpClient.Connect(IPAddress.Parse("***********"), 5050)
            '    Console.WriteLine("TCP Client connected to server.")
            '    Dim listenThread As New Threading.Thread(AddressOf StartListeningToServer)
            '    listenThread.IsBackground = True
            '    listenThread.Start()
            'Catch ex As Exception
            '    Console.WriteLine("TCP Client connection error: " & ex.Message)
            'End Try
            'jswei
            ' Connect to TCP server only for LPR terminals
            If mAntennaType = "lpr" Then
                'Console.WriteLine("LPR Terminal: Initializing TCP connection for LPR data...")
                Try
                    TCPClient.Connect(IPAddress.Parse("***********"), 5050)
                    'Console.WriteLine("LPR Terminal: TCP Client connected to server.")
                    Dim listenThread As New Threading.Thread(AddressOf StartListeningToServer)
                    listenThread.IsBackground = True
                    listenThread.Start()
                Catch ex As Exception
                    Console.WriteLine("LPR Terminal: TCP Client connection error: " & ex.Message)
                End Try
            Else
                Console.WriteLine($"UDP Terminal: Skipping LPR TCP connection for terminal type: {mAntennaType}")
            End If

            ' Allow sending messages to server
            'While True
            '    Dim readline As String = Console.ReadLine()
            '    If Not String.IsNullOrEmpty(readline) AndAlso TcpClient IsNot Nothing AndAlso TcpClient.Connected Then
            '        Dim stream As NetworkStream = TcpClient.GetStream()
            '        Dim sendData As Byte() = Encoding.UTF8.GetBytes(readline)
            '        stream.Write(sendData, 0, sendData.Length)
            '    End If
            'End While


            If TerminalType = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device Then
                'mFullSignOn = mDBManager.IsTerminalBlockedByFull(mTermInformation.TerminalNo)
                Dim subcpn As Short = IIf(mTermInformation.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM, mTermInformation.DestinationSubCarpark, mTermInformation.SourceSubCarpark)
                Dim fsign As Object = mDBManager.FindFullSignReserved(subcpn)
                If fsign Is Nothing Then
                    mFullSignBuffer = 0
                Else
                    mFullSignBuffer = CType(fsign, Short)
                End If
                If FullSignOn = True Then
                    mHDwareMgr.TurnOnFullSign()
                Else
                    mHDwareMgr.TurnOffFullSign()
                End If
            End If

            Dim i As Integer
            For i = 0 To mServices.Count - 1
                Select Case CType(mServices(i), ITerminalService).selftest()
                    Case SelfTestResult.FATALCASHCARDERROR, SelfTestResult.FATALEPSERROR,
                            SelfTestResult.FATALREMERROR
                        Return False
                End Select
            Next

            'HDManager.TerminalType = mTermInformation.TerminalType

            ''zaw disabled LED command for season, since not using. 29/08/2016
            'If mHDwareMgr.LEDEnabled = True Then
            '    If HDManager.CreateLED = False Then
            '        Dim trans As New TransactionMsg
            '        trans.mLog = False
            '        trans.TransactionType = ComCodes.TransactionTypeSelections.CEHardWareSelfTestError
            '        SendMsgToSMC(trans)
            '    End If
            'End If

            If mAddonOption.SubCPWithinSubCP = True Then
                mRelatedFromTerminal = mDBManager.FindRelatedFromTerminal(mTermInformation.TerminalNo)
                If Not mRelatedFromTerminal Is Nothing Then
                    mIsToTerminal = True
                End If
                mRelatedToTerminal = mDBManager.FindRelatedToTerminal(mTermInformation.TerminalNo)
                If Not mRelatedToTerminal Is Nothing Then
                    mIsFromTerminal = True
                End If
            End If

            mAllTerminals = mDBManager.RetrieveAllTerminalsEx()
            For Each ter As Terminal In mAllTerminals
                If mTermInformation.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM Then
                    If ter.DestinationSubCarpark = mTermInformation.DestinationSubCarpark Or ter.SourceSubCarpark = mTermInformation.DestinationSubCarpark Then
                        mTermsWithSameSubCP(ter.TerminalNo) = ter
                    End If
                ElseIf mTermInformation.TerminalType = Enumeration.TERMINALOPTION.EXITTERM Then
                    If ter.DestinationSubCarpark = mTermInformation.SourceSubCarpark Or ter.SourceSubCarpark = mTermInformation.SourceSubCarpark Then
                        mTermsWithSameSubCP(ter.TerminalNo) = ter
                    End If
                End If
            Next

            'mDeviceBone.DeviceNature.MyDeviceType = mTermType
        ElseIf testdbr = Enumeration.SelfTestResult.FATALDATABASEERROR Then
            mSysMode = Enumeration.SystemMode.UNCONFIGURED
        End If

        Return True

    End Function

    Private Sub ServerSniffedEventHandler()
        Dim a As Integer = 1
        If mSysMode = Enumeration.SystemMode.UNCONFIGURED Then
            'mSysMode = Enumeration.SystemMode.STANDBY
            RequestAllInforFromSMC()
        ElseIf mSysMode = Enumeration.SystemMode.ACTIVE Then
            'onlinestatus()
        End If
    End Sub

    Private Sub ReceiveMessageEvtProcess(ByVal msg As basedMessaging, ByVal fromIP As String)
        If mSysMode = Enumeration.SystemMode.ACTIVE Then
            ActiveMsgProcess(msg, fromIP)
        Else
            StandbyMsgProcess(msg, fromIP)
        End If
    End Sub

    Private Sub ActiveMsgProcess(ByVal msg As basedMessaging, ByVal fromIP As String)
        'Select Case mTermType
        '    Case NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device
        '        ActiveEntryMsgProcess(msg, devtype, fromIP)
        '    Case NetworkReferences.DeviceServiceDefinition.DeviceType.Exit_Device
        '        ActiveExitMsgProcess(msg, devtype, fromIP)
        '    Case Else
        'End Select
        DispatchMessage(msg, fromIP)
    End Sub

    Private Sub StandbyMsgProcess(ByVal msg As basedMessaging, ByVal fromIP As String)
        If fromIP = mDeviceBone.SMCIPAddress Then
            'Select Case mTermType
            '    Case NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device
            '        'StandbyEntryMsgProcess(msg, devtype, fromIP)
            '    Case NetworkReferences.DeviceServiceDefinition.DeviceType.Exit_Device
            '        'StandbyExitMsgProcess(msg, devtype, fromIP)
            '    Case NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED
            '    Case Else
            'End Select
            DispatchMessage(msg, fromIP)
        End If
    End Sub

    Private Sub DispatchMessage(ByVal msg As basedMessaging, ByVal fromIP As String)
        If msg.messageType = Enumeration.messageTypeSelections.TransactionMsg Then
            Select Case CType(msg, TransactionMsg).TransactionType
                Case ComCodes.TransactionTypeSelections.HPCReplyDeduct,
                    ComCodes.TransactionTypeSelections.HPCReplyVirtualDeduct,
                    ComCodes.TransactionTypeSelections.HPCReplyNoCashCardInside,
                    ComCodes.TransactionTypeSelections.HPCCashCardBalanceNotEnough,
                    ComCodes.TransactionTypeSelections.HPCReplyIUDeductFailed,
                    ComCodes.TransactionTypeSelections.HPCReplyIUDetectFailed
                    'RaiseEvent MessageHandledByExitFullEPS(msg, fromIP)
                Case 2501 To 2509 'FullEps
                    'RaiseEvent MessageHandledByFullEPS(msg, fromIP)
                Case 2510 To 3500 'FullEps, SemiEps
                    'RaiseEvent MessageHandledByFullEPS(msg, fromIP)
                    RaiseEvent MessageHandledByEPS(msg, fromIP)
                Case 3501 To 4500 'CashCard
                    'RaiseEvent MessageHandledByCashcard(msg, fromIP)
                Case 4501 To 5500 'REM
                    'RaiseEvent MessageHandledByRem(msg, fromIP)
                Case 5501 To 5600 'HPC message
                    'RaiseEvent MessageHandledByFullEPS(msg, fromIP)
                Case 5601 To 5602
                    'handlefullstatus(msg)   'add for full status request
                Case Else
                    ProcessMsgforLocal(msg)
            End Select
        Else
            Console.WriteLine("Debug Error")
        End If
    End Sub

    Private Sub RequestAllInforFromSMC()
        Dim msg As New TransactionMsg
        msg.mToIP = mDeviceBone.SMCIPAddress
        msg.TransactionType = ComCodes.TransactionTypeSelections.RequestAllInformation
        msg.mLog = False
        msg.mDestdevice = NetworkReferences.DeviceServiceDefinition.DeviceType.CMC_Device
        mDeviceBone.SendMsgOut(msg)
    End Sub

    Private Sub handleBulkEvent(ByVal data As Byte(), ByVal ip As String)
        Dim str As String = Text.ASCIIEncoding.ASCII.GetString(data, 0, data.Length)
        Dim strs() As String = str.Split("?")

        Select Case CType(strs(0), TransactionTypeSelections)
            Case ComCodes.TransactionTypeSelections.DownloadAllIUSeasonInfo
                Dim tbtmp As New DataTable
                Dim i As Integer
                For i = 0 To 13
                    tbtmp.Columns.Add()
                Next
                tbtmp.Columns(2).DataType = GetType(DateTime)
                tbtmp.Columns(3).DataType = GetType(DateTime)
                For ii As Integer = 1 To strs.Length - 1
                    Dim rw As DataRow = tbtmp.NewRow
                    Dim strss() As String = strs(ii).Split(";")
                    rw(0) = strss(0)
                    rw(2) = DateTime.ParseExact(strss(1), "dd/MM/yyyy HH:mm:ss", Nothing)
                    rw(3) = DateTime.ParseExact(strss(2), "dd/MM/yyyy HH:mm:ss", Nothing)
                    If strss(3) <> String.Empty Then
                        rw(9) = CType(strss(3), Short)
                    End If

                    If strss(4) <> String.Empty Then
                        rw(10) = CType(strss(4), Short)
                    End If

                    rw(11) = CType(strss(5), Boolean)
                    rw(12) = CType(strss(6), Boolean)
                    tbtmp.Rows.Add(rw)
                Next
                str = Nothing
                strs = Nothing
                'Dim num As Integer = mDBManager.AddIUSeasonRecordsEx(tbtmp)
                'If num <> 0 Then
                '    ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyIUSeasonCount, num)
                'End If
            Case ComCodes.TransactionTypeSelections.DownloadIUSeasonPermits
                'cnt = 0
                Dim tbtmp As New DataTable
                Dim i As Integer
                For i = 0 To 3
                    tbtmp.Columns.Add()
                Next
                For ia As Integer = 1 To strs.Length - 1
                    Dim str2() As String = strs(ia).Split(";")
                    Dim rw As DataRow = tbtmp.NewRow
                    rw(0) = CType(str2(0), Integer)
                    rw(1) = str2(1)
                    rw(2) = CType(str2(2), Short)
                    rw(3) = CType(str2(3), Short)
                    tbtmp.Rows.Add(rw)
                Next
                'Dim cnt As Integer = mDBManager.AddIUSeasonPermitsRecords(tbtmp)
                'If cnt <> 0 Then
                '    ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyIUSeasonPermitsCount, cnt)
                'End If
            Case ComCodes.TransactionTypeSelections.DownloadAllInformation
                If mSysMode = SystemMode.UNCONFIGURED Then
                    mSysMode = Enumeration.SystemMode.STANDBY
                    'Try
                    ConfigSystemByAllInformation(strs)
                    'Catch ex As Exception
                    'Console.Write(True)
                    'End Try

                End If
            Case ComCodes.TransactionTypeSelections.InformationSyncronize
                DoTheSyncronization(strs)
            Case ComCodes.TransactionTypeSelections.SyncRunningRecord
                SyncRunningRec(strs)
            Case ComCodes.TransactionTypeSelections.UpdateGroupSeasonDetails
                UpdateGroupSeasonDetails(strs)
        End Select

    End Sub

    Private Sub ConfigSystemByAllInformation(ByVal strs() As String)
        Dim cccinterv As Integer
        Dim btm As Integer
        'Threading.Thread.CurrentThread.Priority = Threading.ThreadPriority.Highest
        Dim cnt As Integer = 0
        For ii As Integer = 1 To strs.Length - 1
            Dim str1() As String = strs(ii).Split("|")
            Select Case CType(str1(0), TransactionTypeSelections)
                Case ComCodes.TransactionTypeSelections.DownloadSubCarparkInfo
                    cnt = 0
                    Dim tbtmp As New DataTable
                    Dim i As Integer
                    For i = 0 To 6
                        tbtmp.Columns.Add()
                    Next
                    For ia As Integer = 1 To str1.Length - 1
                        Dim str2() As String = str1(ia).Split(";")
                        Dim rw As DataRow = tbtmp.NewRow
                        rw(0) = str2(0)
                        rw(6) = str2(1)
                        rw(2) = str2(2)
                        rw(3) = str2(3)
                        rw(4) = str2(4)
                        rw(5) = Integer.Parse(CType(str2(5), String))
                        tbtmp.Rows.Add(rw)
                    Next
                    cnt = mDBManager.AddSubCarparkRecords(tbtmp)
                    If cnt <> 0 Then
                        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplySubCarparkCount, cnt)
                    End If
                Case ComCodes.TransactionTypeSelections.DownloadTerminalInfo
                    cnt = 0
                    Dim tbtmp As New DataTable
                    Dim i As Integer
                    For i = 0 To 9
                        tbtmp.Columns.Add()
                    Next
                    For ia As Integer = 1 To str1.Length - 1
                        Dim str2() As String = str1(ia).Split(";")
                        Dim rw As DataRow = tbtmp.NewRow
                        rw(0) = str2(0)
                        rw(2) = str2(1)
                        rw(3) = str2(3)
                        rw(4) = str2(4)
                        rw(5) = str2(2)
                        rw(6) = str2(5)
                        rw(8) = str2(6)
                        rw(9) = str2(7)
                        rw(1) = str2(8) 'add on 10/07/08
                        tbtmp.Rows.Add(rw)
                        If rw(6) = mLocalIPaddress Then
                            mTermInformation.CarparkNo = rw(5)
                            mTermInformation.IPAddress = rw(6)
                            mTermInformation.SourceSubCarpark = CType(rw(3), Short)
                            mTermInformation.DestinationSubCarpark = CType(rw(4), Short)
                            mTermInformation.TerminalNo = rw(0)
                            mTermInformation.TerminalType = rw(2)
                            mTermInformation.RateSetNo = IIf(String.IsNullOrEmpty(rw(8)), -1, rw(8))
                            mTermInformation.SeasonOnly = str2(7)
                            mTermInformation.TerminalName = str2(8) 'add on 10/07/08

                            Select Case mTermInformation.TerminalType
                                Case Enumeration.TERMINALOPTION.ENTRYTERM
                                    mTermType = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device
                                    mDeviceBone.IgnoreHPC = True
                                Case Enumeration.TERMINALOPTION.EXITTERM
                                    mTermType = NetworkReferences.DeviceServiceDefinition.DeviceType.Exit_Device
                                Case Enumeration.TERMINALOPTION.HPC
                                    mTermType = NetworkReferences.DeviceServiceDefinition.DeviceType.HPC_Device
                            End Select
                        End If
                    Next
                    cnt = mDBManager.AddTerminalRecords(tbtmp)
                    If cnt <> 0 Then
                        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyTerminalCount, cnt)
                    End If
                Case ComCodes.TransactionTypeSelections.DownloadTerminalRelation
                    cnt = 0
                    Dim tbtmp As New DataTable
                    Dim i As Integer
                    For i = 0 To 4
                        tbtmp.Columns.Add()
                    Next
                    For ia As Integer = 1 To str1.Length - 1
                        Dim str2() As String = str1(ia).Split(";")
                        Dim rw As DataRow = tbtmp.NewRow
                        rw(0) = str2(0)
                        rw(1) = str2(1)
                        rw(2) = str2(2)
                        rw(3) = str2(3)
                        rw(4) = str2(4)
                        tbtmp.Rows.Add(rw)
                    Next
                    cnt = mDBManager.AddTerminalRelationRecords(tbtmp)
                    If cnt <> 0 Then
                        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyTerminalRelationCount, cnt)
                    End If
                Case ComCodes.TransactionTypeSelections.DownloadWorkingMode
                    cnt = 0
                    Dim tbtmp As New DataTable
                    Dim i As Integer
                    For i = 0 To 5
                        tbtmp.Columns.Add()
                    Next
                    For ia As Integer = 1 To str1.Length - 1
                        Dim str2() As String = str1(ia).Split(";")
                        Dim rw As DataRow = tbtmp.NewRow
                        For i = 0 To 5
                            rw(i) = str2(i)
                        Next
                        tbtmp.Rows.Add(rw)
                    Next
                    cnt = mDBManager.AddWorkingModes(tbtmp)
                    If cnt <> 0 Then
                        Dim workmds() As WorkingMode
                        Dim subcbn As Short = IIf(mTermInformation.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM, mTermInformation.DestinationSubCarpark, mTermInformation.SourceSubCarpark)
                        If mDBManager.GetWorkingModeForTerminal(mTermInformation.CarparkNo, subcbn, workmds) = True Then
                            GenerateServices(workmds)
                        End If
                        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyWorkingModeCount, cnt)
                    End If
                Case ComCodes.TransactionTypeSelections.DownloadComplimentary
                    'cnt = 0
                    'Dim tbtmp As New DataTable
                    'Dim i As Integer
                    'For i = 0 To 3
                    '    tbtmp.Columns.Add()
                    'Next
                    'For ia As Integer = 1 To str1.Length - 1
                    '    Dim str2() As String = str1(ia).Split(";")
                    '    Dim rw As DataRow = tbtmp.NewRow
                    '    rw(0) = str2(0)
                    '    rw(1) = DateTime.ParseExact(CType(str2(1), String), "dd/MM/yyyy HH:mm:ss", Nothing)
                    '    rw(2) = DateTime.ParseExact(CType(str2(2), String), "dd/MM/yyyy HH:mm:ss", Nothing)
                    '    rw(3) = CType(str2(3), Short)
                    '    tbtmp.Rows.Add(rw)
                    '    Dim tmpcom As New Complimentary
                    '    tmpcom.ComplimentaryNo = str2(0)
                    '    tmpcom.ValidFrom = rw(1)
                    '    tmpcom.ValidTo = rw(2)
                    '    tmpcom.ComplimentaryType = rw(3)
                    '    mComplimentaryHash(tmpcom.ComplimentaryNo) = tmpcom
                    'Next
                    'cnt = mDBManager.AddComplimentaryRecords(tbtmp)
                    'If cnt <> 0 Then
                    '    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyComplimentaryCount, cnt)
                    'End If
                Case ComCodes.TransactionTypeSelections.DownloadRedemption
                    'cnt = 0
                    'Dim tbtmp As New DataTable
                    'Dim i As Integer
                    'For i = 0 To 5
                    '    tbtmp.Columns.Add()
                    'Next
                    'For ia As Integer = 1 To str1.Length - 1
                    '    Dim str2() As String = str1(ia).Split(";")
                    '    Dim rw As DataRow = tbtmp.NewRow
                    '    rw(0) = str2(0)
                    '    rw(1) = DateTime.ParseExact(CType(str2(1), String), "dd/MM/yyyy HH:mm:ss", Nothing)
                    '    rw(2) = DateTime.ParseExact(CType(str2(2), String), "dd/MM/yyyy HH:mm:ss", Nothing)
                    '    rw(3) = CType(str2(3), Single)
                    '    rw(4) = str2(4)
                    '    rw(5) = str2(5)
                    '    tbtmp.Rows.Add(rw)
                    '    Dim tmprede As New Redemption
                    '    tmprede.RedemptionNo = str2(0)
                    '    tmprede.ValidFrom = rw(1)
                    '    tmprede.ValidTo = rw(2)
                    '    tmprede.Value = rw(3)
                    '    tmprede.Valuetype = rw(4)
                    '    tmprede.RedemptionType = rw(5)
                    '    mRedemptionHash(tmprede.RedemptionNo) = tmprede
                    'Next
                    'cnt = mDBManager.AddRedemptionRecords(tbtmp)
                    'If cnt <> 0 Then
                    '    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyRedemptionCount, cnt)
                    'End If
                Case ComCodes.TransactionTypeSelections.DownloadGroupSeasonPermits
                    cnt = 0
                    Dim tbtmp As New DataTable
                    Dim i As Integer
                    For i = 0 To 3
                        tbtmp.Columns.Add()
                    Next
                    For ia As Integer = 1 To str1.Length - 1
                        Dim str2() As String = str1(ia).Split(";")
                        Dim rw As DataRow = tbtmp.NewRow
                        rw(0) = CType(str2(0), Integer)
                        rw(1) = CType(str2(1), Integer)
                        rw(2) = CType(str2(2), Short)
                        rw(3) = CType(str2(3), Short)
                        UpdateGroupSeasonPermits(rw)
                    Next
                    cnt = mDBManager.AddGroupSeasonPermitsRecords(tbtmp)
                    If cnt <> 0 Then
                        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyGroupSeasonPermitsCount, cnt)
                    End If
                Case ComCodes.TransactionTypeSelections.DownloadSeasonGroup
                    cnt = 0
                    Dim tbtmp As New DataTable
                    Dim i As Integer
                    For i = 0 To 8
                        tbtmp.Columns.Add()
                    Next
                    For ia As Integer = 1 To str1.Length - 1
                        Dim str2() As String = str1(ia).Split(";")
                        Dim rw As DataRow = tbtmp.NewRow
                        rw(0) = str2(0)
                        rw(2) = str2(1)
                        rw(3) = str2(2)
                        rw(4) = str2(3)
                        tbtmp.Rows.Add(rw)
                        Dim tmpSeasgrp As New ClsSeasonGroup
                        tmpSeasgrp.GroupNo = str2(0)
                        tmpSeasgrp.Maximum = str2(1)
                        tmpSeasgrp.Threshold = str2(2)
                        tmpSeasgrp.CurrentCount = str2(3)
                        mSeasonGrpHash(tmpSeasgrp.GroupNo) = tmpSeasgrp
                    Next
                    cnt = mDBManager.AddSeasonGroupRecords(tbtmp)
                    If cnt <> 0 Then
                        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplySeasonGroupCount, cnt)
                    End If
                Case ComCodes.TransactionTypeSelections.DownloadHoliday
                    'cnt = 0
                    'Dim tbtmp As New DataTable
                    'Dim i As Integer
                    'For i = 0 To 1
                    '    tbtmp.Columns.Add()
                    'Next
                    'For ia As Integer = 1 To str1.Length - 1
                    '    Dim str2() As String = str1(ia).Split(";")
                    '    Dim rw As DataRow = tbtmp.NewRow
                    '    rw(0) = str2(0)
                    '    rw(1) = str2(1)
                    '    tbtmp.Rows.Add(rw)
                    'Next
                    'cnt = mDBManager.AddHolidayRecords(tbtmp)
                    'If cnt <> 0 Then
                    '    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyHolidayCount, cnt)
                    'End If
                Case ComCodes.TransactionTypeSelections.DownloadRateSet
                    'cnt = 0
                    'Dim tbtmp As New DataTable
                    'Dim i As Integer
                    'For i = 0 To 7
                    '    tbtmp.Columns.Add()
                    'Next
                    'For ia As Integer = 1 To str1.Length - 1
                    '    Dim str2() As String = str1(ia).Split(";")
                    '    Dim rw As DataRow = tbtmp.NewRow
                    '    rw(0) = str2(0)
                    '    rw(1) = CType(str2(1), Integer)
                    '    rw(2) = CType(str2(2), Integer)
                    '    rw(3) = CType(str2(3), Integer)
                    '    rw(4) = CType(str2(4), Integer)
                    '    rw(5) = CType(str2(5), Integer)
                    '    rw(6) = CType(str2(6), Integer)
                    '    rw(7) = CType(str2(7), Integer)
                    '    tbtmp.Rows.Add(rw)
                    'Next
                    'cnt = mDBManager.AddRateSetRecords(tbtmp)
                    'If cnt <> 0 Then
                    '    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyRateSetCount, cnt)
                    'End If
                Case ComCodes.TransactionTypeSelections.DownloadCyclingBlockList
                    'cnt = 0
                    'Dim tbtmp As New DataTable
                    'Dim i As Integer
                    'For i = 0 To 9
                    '    tbtmp.Columns.Add()
                    'Next
                    'For ia As Integer = 1 To str1.Length - 1
                    '    Dim str2() As String = str1(ia).Split(";")
                    '    Dim rw As DataRow = tbtmp.NewRow
                    '    rw(0) = CType(str2(0), Integer)
                    '    rw(1) = CType(str2(1), Integer)
                    '    rw(2) = str2(2)
                    '    rw(3) = str2(3)
                    '    rw(4) = CType(str2(4), Boolean)
                    '    rw(5) = CType(str2(5), Single)
                    '    rw(6) = CType(str2(6), Single)
                    '    rw(7) = CType(str2(7), Integer)
                    '    rw(8) = CType(str2(8), Integer)
                    '    rw(9) = CType(str2(9), Integer)
                    '    tbtmp.Rows.Add(rw)
                    'Next
                    'cnt = mDBManager.AddCyclingBlockList(tbtmp)
                    'If cnt <> 0 Then
                    '    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyCyclingBlockListCount, cnt)
                    'End If
                Case ComCodes.TransactionTypeSelections.DownloadDiscreteBlockList
                    'cnt = 0
                    'Dim tbtmp As New DataTable
                    'Dim i As Integer
                    'For i = 0 To 9
                    '    tbtmp.Columns.Add()
                    'Next
                    'For ia As Integer = 1 To str1.Length - 1
                    '    Dim str2() As String = str1(ia).Split(";")
                    '    Dim rw As DataRow = tbtmp.NewRow
                    '    rw(0) = CType(str2(0), Integer)
                    '    rw(1) = CType(str2(1), Integer)
                    '    rw(2) = str2(2)
                    '    rw(3) = str2(3)
                    '    rw(4) = CType(str2(4), Boolean)
                    '    rw(5) = CType(str2(5), Single)
                    '    rw(6) = CType(str2(6), Single)
                    '    rw(7) = CType(str2(7), Integer)
                    '    rw(8) = CType(str2(8), Integer)
                    '    rw(9) = CType(str2(9), Integer)
                    '    tbtmp.Rows.Add(rw)
                    'Next
                    'cnt = mDBManager.AddDiscreteBlockList(tbtmp)
                    'If cnt <> 0 Then
                    '    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyAllDiscreteBlockListCount, cnt)
                    'End If
                Case ComCodes.TransactionTypeSelections.DownloadAdvancedBlockList
                    'cnt = 0
                    'Dim tbtmp As New DataTable
                    'Dim i As Integer
                    'For i = 0 To 5
                    '    tbtmp.Columns.Add()
                    'Next
                    'For ia As Integer = 1 To str1.Length - 1
                    '    Dim str2() As String = str1(ia).Split(";")
                    '    Dim rw As DataRow = tbtmp.NewRow
                    '    For i = 0 To 5
                    '        rw(i) = str2(i)
                    '    Next
                    '    tbtmp.Rows.Add(rw)
                    'Next
                    'cnt = mDBManager.AddAdvancedBlockList(tbtmp)
                    'If cnt <> 0 Then
                    '    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyAdvancedBlockListCount, cnt)
                    'End If
                Case ComCodes.TransactionTypeSelections.DownloadRateComposite
                    'cnt = 0
                    'Dim tbtmp As New DataTable
                    'Dim i As Integer
                    'For i = 0 To 2
                    '    tbtmp.Columns.Add()
                    'Next
                    'For ia As Integer = 1 To str1.Length - 1
                    '    Dim str2() As String = str1(ia).Split(";")
                    '    Dim rw As DataRow = tbtmp.NewRow
                    '    rw(0) = CType(str2(0), Integer)
                    '    rw(1) = CType(str2(1), Boolean)
                    '    rw(2) = str2(2)
                    '    tbtmp.Rows.Add(rw)
                    'Next
                    'cnt = mDBManager.AddRateComposite(tbtmp)
                    'If cnt <> 0 Then
                    '    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyRateCompositeCount, cnt)
                    'End If
                Case ComCodes.TransactionTypeSelections.DownloadMainCarparkConfig
                    cnt = 0
                    Dim tbtmp As New DataTable
                    Dim i As Integer
                    For i = 0 To 6
                        tbtmp.Columns.Add()
                    Next
                    Dim rw As DataRow = tbtmp.NewRow
                    'Console.WriteLine("-------------- str1 is " & str1(1))
                    Dim str2() As String = str1(1).Split(";")
                    rw(0) = str2(0)
                    rw(3) = str2(1)
                    rw(4) = str2(2)
                    tbtmp.Rows.Add(rw)
                    cnt = mDBManager.AddMainCarparkConfig(tbtmp)
                    If cnt <> 0 Then
                        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyMainCarparkConfigCount, cnt)
                    End If
                Case ComCodes.TransactionTypeSelections.DownloadReceiptMsg
                    'cnt = 0
                    'Dim tbtmp As New DataTable
                    'Dim i As Integer
                    'For i = 0 To 4
                    '    tbtmp.Columns.Add()
                    'Next
                    'Dim rw As DataRow = tbtmp.NewRow
                    'Dim str2() As String = str1(1).Split(";")
                    'rw(0) = str2(0)
                    'rw(1) = str2(1)
                    'rw(2) = str2(2)
                    'rw(3) = str2(3)
                    'rw(4) = CType(str2(4), Boolean)
                    'tbtmp.Rows.Add(rw)
                    ''Add to database
                    'cnt = mDBManager.AddReceiptMessage(tbtmp)
                    'If cnt <> 0 Then
                    '    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyReceiptMsgCount, cnt)
                    'End If
                Case ComCodes.TransactionTypeSelections.DownloadCyclingCapList
                    'cnt = 0
                    'Dim tbtmp As New DataTable
                    'Dim i As Integer
                    'For i = 0 To 4
                    '    tbtmp.Columns.Add()
                    'Next
                    'For ia As Integer = 1 To str1.Length - 1
                    '    Dim str2() As String = str1(ia).Split(";")
                    '    Dim rw As DataRow = tbtmp.NewRow
                    '    rw(0) = CType(str2(0), Integer)
                    '    rw(1) = str2(1)
                    '    rw(2) = str2(2)
                    '    rw(3) = CType(str2(3), Single)
                    '    rw(4) = CType(str2(4), Integer)
                    '    tbtmp.Rows.Add(rw)
                    'Next
                    'cnt = mDBManager.AddCyclingCapList(tbtmp)
                    'If cnt <> 0 Then
                    '    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyCyclingCapListCount, cnt)
                    'End If
                Case ComCodes.TransactionTypeSelections.DownloadCrossOverList
                    'cnt = 0
                    'Dim tbtmp As New DataTable
                    'Dim i As Integer
                    'For i = 0 To 5
                    '    tbtmp.Columns.Add()
                    'Next
                    'For ia As Integer = 1 To str1.Length - 1
                    '    Dim str2() As String = str1(ia).Split(";")
                    '    Dim rw As DataRow = tbtmp.NewRow
                    '    rw(0) = CType(str2(0), Integer)
                    '    rw(1) = CType(str2(1), Integer)
                    '    rw(2) = str2(2)
                    '    rw(3) = CType(str2(3), Integer)
                    '    rw(4) = CType(str2(4), Integer)
                    '    rw(5) = CType(str2(5), Integer)
                    '    tbtmp.Rows.Add(rw)
                    'Next
                    'cnt = mDBManager.AddCrossOverList(tbtmp)
                    'If cnt <> 0 Then
                    '    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyCrossOverListCount, cnt)
                    'End If
                Case ComCodes.TransactionTypeSelections.DownloadCyclingRateList
                    'cnt = 0
                    'Dim tbtmp As New DataTable
                    'Dim i As Integer
                    'For i = 0 To 9
                    '    tbtmp.Columns.Add()
                    'Next
                    'For ia As Integer = 1 To str1.Length - 1
                    '    Dim str2() As String = str1(ia).Split(";")
                    '    Dim rw As DataRow = tbtmp.NewRow
                    '    rw(0) = CType(str2(0), Integer)
                    '    rw(1) = CType(str2(1), Integer)
                    '    rw(2) = CType(str2(2), Integer)
                    '    rw(3) = CType(str2(3), Boolean)
                    '    rw(4) = CType(str2(4), Integer)
                    '    rw(5) = CType(str2(5), Integer)
                    '    rw(6) = CType(str2(6), Integer)
                    '    rw(7) = CType(str2(7), Integer)
                    '    rw(8) = CType(str2(8), String)
                    '    rw(9) = CType(str2(9), Boolean)
                    '    tbtmp.Rows.Add(rw)
                    'Next
                    'cnt = mDBManager.AddCyclingRateList(tbtmp)
                    'If cnt <> 0 Then
                    '    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyCyclingRateListCount, cnt)
                    'End If
                Case ComCodes.TransactionTypeSelections.DownloadPGSInfo
                    cnt = 0
                    Dim tbtmp As New DataTable
                    Dim i As Integer
                    For i = 0 To 2
                        tbtmp.Columns.Add()
                    Next
                    For ia As Integer = 1 To str1.Length - 1
                        Dim str2() As String = str1(ia).Split(";")
                        Dim rw As DataRow = tbtmp.NewRow
                        rw(0) = str2(0)
                        rw(1) = str2(1)
                        rw(2) = str2(2)
                        tbtmp.Rows.Add(rw)
                    Next
                    cnt = mDBManager.AddPGSRecords(tbtmp)
                    If cnt <> 0 Then
                        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyPGSInfoCount, cnt)
                    End If
                Case ComCodes.TransactionTypeSelections.DownloadAddonOption
                    Dim addon As AddonOption
                    Dim str2() As String = str1(1).Split(";"c)
                    addon.LEDEnabled = str2(0)
                    addon.ShutterEnabled = str2(1)
                    addon.CashcardConfirm = str2(2)
                    addon.PowerFailAlarm = str2(3)
                    addon.SeasonAllowedWhenFull = str2(4)
                    addon.TailGateSensor = str2(5)
                    addon.SubCPWithinSubCP = str2(6)
                    'Add to database
                    cnt = mDBManager.AddAddonOptionRecords(addon)
                    If cnt <> 0 Then
                        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyAddonOptionCount, cnt)
                    End If
                Case ComCodes.TransactionTypeSelections.RequestPGSEnabled
                    mDBManager.AddPGSEnabled(CType(str1(1), Boolean))
                    'Dim themsg As New TransactionMsg
                    'themsg.TransactionType = ComCodes.TransactionTypeSelections.ReplyPGSEnabled
                    'themsg.message1 = mDBManager.PGSIsEnabled()
                    'themsg.mLog = True
                    'SendMsgToSMC(themsg)
                Case ComCodes.TransactionTypeSelections.DownloadAllFullStatus
                    cnt = 0
                    Dim tbtmp As New DataTable
                    Dim i As Integer
                    For i = 0 To 1
                        tbtmp.Columns.Add()
                    Next
                    For ia As Integer = 1 To str1.Length - 1
                        Dim str2() As String = str1(ia).Split(";")
                        Dim rw As DataRow = tbtmp.NewRow
                        rw(0) = str2(0)
                        rw(1) = str2(1)
                        tbtmp.Rows.Add(rw)
                    Next
                    cnt = mDBManager.AddFullStatusRecords(tbtmp)
                    If cnt <> 0 Then
                        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyAllFullStatusCount, cnt)
                    End If
                Case ComCodes.TransactionTypeSelections.DownloadAllIUSeasonInfo
                    cnt = 0
                    Dim tbtmp As New DataTable
                    tbtmp.Columns.AddRange(mDBManager.GetIUSeasonDataColumns)

                    Dim strln As Integer = str1.Length - 1
                    'Dim pri As Threading.ThreadPriority = Threading.Thread.CurrentThread.Priority
                    Dim bdate As Date = Now
                    For ia As Integer = 1 To strln
                        Dim str2() As String = str1(ia).Split(";")
                        Dim rw As DataRow = tbtmp.NewRow
                        rw(0) = str2(0)
                        rw(2) = DateTime.ParseExact(str2(1), "dd/MM/yyyy HH:mm:ss", Nothing)
                        rw(3) = DateTime.ParseExact(str2(2), "dd/MM/yyyy HH:mm:ss", Nothing)

                        rw(4) = str2(8)

                        If str2(3) <> String.Empty Then
                            rw(9) = CType(str2(3), Short)
                        End If

                        If str2(4) <> String.Empty Then
                            rw(10) = CType(str2(4), Short)
                        End If

                        rw(11) = CType(str2(5), Boolean)
                        rw(12) = CType(str2(6), Boolean)
                        tbtmp.Rows.Add(rw)
                        Eps.UpdateSeason(rw)
                        'Dim ontime As Integer = Now.Subtract(bdate).TotalMilliseconds
                        'Console.WriteLine("time: " & ontime & "---" & ia)
                    Next

                    btm = Now.Subtract(bdate).TotalMilliseconds
                    Dim ccc As DateTime = Now
                    'Threading.Thread.CurrentThread.Priority = pri
                    cnt = mDBManager.AddIUSeasonRecordsEx(tbtmp)
                    cccinterv = Now.Subtract(ccc).TotalMilliseconds
                    If cnt <> 0 Then
                        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyIUSeasonCount, cnt)
                    End If
                Case ComCodes.TransactionTypeSelections.DownloadCashCardSeasonInfo
                    'If IsNothing(CashCardSys) = False Then
                    '    cnt = 0
                    '    Dim tbtmp As New DataTable
                    '    'Dim i As Integer
                    '    tbtmp.Columns.AddRange(mDBManager.GetCCSeasonDataColumns)
                    '    'For i = 0 To 12
                    '    '    tbtmp.Columns.Add()
                    '    'Next
                    '    For ia As Integer = 1 To str1.Length - 1
                    '        Dim str2() As String = str1(ia).Split(";")
                    '        Dim rw As DataRow = tbtmp.NewRow
                    '        rw(0) = str2(0)
                    '        rw(1) = DateTime.ParseExact(str2(1), "dd/MM/yyyy HH:mm:ss", Nothing)
                    '        rw(2) = DateTime.ParseExact(str2(2), "dd/MM/yyyy HH:mm:ss", Nothing)
                    '        If str2(3) <> String.Empty Then
                    '            rw(8) = CType(str2(3), Short)
                    '        End If

                    '        If str2(4) <> String.Empty Then
                    '            rw(9) = CType(str2(4), Short)
                    '        End If

                    '        rw(10) = CType(str2(5), Boolean)
                    '        rw(11) = CType(str2(6), Boolean)
                    '        tbtmp.Rows.Add(rw)
                    '        CashCardSys.UpdateSeason(rw)
                    '    Next
                    '    cnt = mDBManager.AddCashCardSeasonInfoRecords(tbtmp)
                    '    If cnt <> 0 Then
                    '        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyCashCardSeasonCount, cnt)
                    '    End If
                    'End If
                Case ComCodes.TransactionTypeSelections.DownloadCashCardSeasonPermits
                    'If IsNothing(CashCardSys) = False Then
                    '    cnt = 0
                    '    Dim tbtmp As New DataTable
                    '    Dim i As Integer
                    '    For i = 0 To 3
                    '        tbtmp.Columns.Add()
                    '    Next
                    '    For ia As Integer = 1 To str1.Length - 1
                    '        Dim str2() As String = str1(ia).Split(";")
                    '        Dim rw As DataRow = tbtmp.NewRow
                    '        rw(0) = CType(str2(0), Integer)
                    '        rw(1) = str2(1)
                    '        rw(2) = CType(str2(2), Short)
                    '        rw(3) = CType(str2(3), Short)
                    '        tbtmp.Rows.Add(rw)
                    '        CashCardSys.UpdateSeasonPermits(rw)
                    '    Next
                    '    cnt = mDBManager.AddCashCardSeasonPermitsRecords(tbtmp)
                    '    If cnt <> 0 Then
                    '        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyCashCardSeasonPermitsCount, cnt)
                    '    End If
                    'End If
                Case ComCodes.TransactionTypeSelections.DownloadAntennaInfo
                    cnt = 0
                    Dim tbtmp As New DataTable
                    Dim i As Integer
                    For i = 0 To 5
                        tbtmp.Columns.Add()
                    Next
                    For ia As Integer = 1 To str1.Length - 1
                        Dim str2() As String = str1(ia).Split(";")
                        Dim rw As DataRow = tbtmp.NewRow
                        rw(0) = str2(0)
                        rw(1) = str2(1)
                        rw(3) = str2(2)
                        rw(4) = str2(3)
                        rw(5) = IIf(str2(4) = "", DBNull.Value, str2(4))
                        tbtmp.Rows.Add(rw)
                    Next
                    cnt = mDBManager.AddAntennaConfigRecords(tbtmp)
                    If cnt <> 0 Then
                        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyAntennaCount, cnt)
                    End If
                Case ComCodes.TransactionTypeSelections.DownloadCHUInfo
                    'cnt = 0
                    'Dim tbtmp As New DataTable
                    'Dim i As Integer
                    'For i = 0 To 8
                    '    tbtmp.Columns.Add()
                    'Next
                    'For ia As Integer = 1 To str1.Length - 1
                    '    Dim str2() As String = str1(ia).Split(";")
                    '    Dim rw As DataRow = tbtmp.NewRow
                    '    rw(0) = str2(0)
                    '    rw(2) = str2(1)
                    '    rw(3) = str2(2)
                    '    rw(4) = str2(3)
                    '    rw(5) = CType(str2(4), Integer)
                    '    rw(6) = CType(str2(5), Integer)
                    '    rw(7) = CType(str2(6), Integer)
                    '    rw(8) = CType(str2(7), Integer)
                    '    tbtmp.Rows.Add(rw)
                    'Next
                    'cnt = mDBManager.AddCHUConfigRecords(tbtmp)
                    'If cnt <> 0 Then
                    '    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyCHUCount, cnt)
                    'End If
                Case ComCodes.TransactionTypeSelections.DownloadIUSeasonPermits
                    cnt = 0
                    Dim tbtmp As New DataTable
                    Dim i As Integer
                    For i = 0 To 3
                        tbtmp.Columns.Add()
                    Next
                    For ia As Integer = 1 To str1.Length - 1
                        Dim str2() As String = str1(ia).Split(";")
                        Dim rw As DataRow = tbtmp.NewRow
                        rw(0) = CType(str2(0), Integer)
                        rw(1) = str2(1)
                        rw(2) = CType(str2(2), Short)
                        rw(3) = CType(str2(3), Short)
                        tbtmp.Rows.Add(rw)
                        Eps.UpdateSeasonPermits(rw)
                    Next
                    cnt = mDBManager.AddIUSeasonPermitsRecords(tbtmp)
                    If cnt <> 0 Then
                        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyIUSeasonPermitsCount, cnt)
                    End If
                Case ComCodes.TransactionTypeSelections.DownloadRunningSeason
                    cnt = 0
                    Dim tbtmp As New DataTable
                    Dim i As Integer
                    For i = 0 To 6
                        tbtmp.Columns.Add()
                    Next
                    tbtmp.Columns(2).DataType = GetType(DateTime)
                    For ia As Integer = 1 To str1.Length - 1
                        Dim str2() As String = str1(ia).Split(";")
                        Dim rw As DataRow = tbtmp.NewRow
                        rw(1) = str2(0)
                        rw(2) = DateTime.ParseExact(str2(1), "dd/MM/yyyy HH:mm:ss", Nothing)
                        rw(3) = str2(2)
                        rw(4) = str2(3)
                        rw(5) = IIf(str2(4) = "", 0, str2(4))
                        rw(6) = str2(5)
                        tbtmp.Rows.Add(rw)
                        Dim tmphor As New RunningSeason
                        tmphor.SeasonNo = str2(0)
                        tmphor.EntryTime = DateTime.ParseExact(str2(1), "dd/MM/yyyy HH:mm:ss", Nothing)
                        tmphor.TerminalNo = str2(2)
                        tmphor.SeasonOption = str2(3)
                        tmphor.SeasonType = IIf(str2(4) = "", -1, str2(4))
                        tmphor.Status = str2(5)
                        mRunningSeasonRecords(tmphor.SeasonNo) = tmphor
                    Next
                    cnt = mDBManager.AddRunningSeasonRecordsEx(tbtmp)
                    If cnt <> 0 Then
                        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyIUSeasonPermitsCount, cnt)
                    End If
                Case ComCodes.TransactionTypeSelections.DownloadRunningHourly
                    'cnt = 0
                    'Dim tbtmp As New DataTable
                    'Dim i As Integer
                    'For i = 0 To 5
                    '    tbtmp.Columns.Add()
                    'Next
                    'For ia As Integer = 1 To str1.Length - 1
                    '    Dim str2() As String = str1(ia).Split(";")
                    '    Dim rw As DataRow = tbtmp.NewRow
                    '    rw(1) = str2(0)
                    '    rw(2) = DateTime.ParseExact(str2(1), "dd/MM/yyyy HH:mm:ss", Nothing)
                    '    rw(3) = str2(2)
                    '    rw(4) = str2(3)
                    '    rw(5) = str2(4)
                    '    tbtmp.Rows.Add(rw)
                    '    Dim tmphor As New RunningHourly
                    '    tmphor.HourlyNo = str2(0)
                    '    tmphor.EntryTime = DateTime.ParseExact(str2(1), "dd/MM/yyyy HH:mm:ss", Nothing)
                    '    tmphor.TerminalNo = str2(2)
                    '    tmphor.HourlyType = str2(3)
                    '    tmphor.Status = str2(4)
                    '    mRunningHourlyRecords(tmphor.HourlyNo) = tmphor
                    'Next
                    'cnt = mDBManager.AddRunningHourlyRecordsEx(tbtmp)
                    'If cnt <> 0 Then
                    '    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyIUSeasonPermitsCount, cnt)
                    'End If
                Case ComCodes.TransactionTypeSelections.DownloadAllBlacklistedTicket
                    cnt = 0
                    Dim tbtmp As New DataTable
                    Dim i As Integer
                    For i = 0 To 5
                        tbtmp.Columns.Add()
                    Next
                    For ia As Integer = 1 To str1.Length - 1
                        Dim str2() As String = str1(ia).Split(";")
                        Dim rw As DataRow = tbtmp.NewRow
                        For i = 0 To 5
                            If i = 3 Or i = 2 Then
                                rw(i) = Date.ParseExact(str2(i), "dd/MM/yyyy", Nothing)
                            Else
                                rw(i) = CType(str2(i), String).Trim()
                            End If
                        Next
                        tbtmp.Rows.Add(rw)
                        Dim blklsted As BlacklistedTicket
                        blklsted.TicketNo = str2(0).Trim()
                        blklsted.TicketType = str2(1).Trim()
                        blklsted.ListedDate = Date.ParseExact(str2(2), "dd/MM/yyyy", Nothing)
                        blklsted.EffectiveDate = Date.ParseExact(str2(3), "dd/MM/yyyy", Nothing)
                        UpdateBlackListed(blklsted)
                    Next
                    cnt = mDBManager.AddBlacklistedTicketRecord(tbtmp)
                    If cnt <> 0 Then
                        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyBlacklistedTicketCount, cnt)
                    End If
                Case ComCodes.TransactionTypeSelections.EnableCCTV
                    mCCTVEnabled = CType(str1(1), Boolean)
                    'mDBManager.AddCCTVEnabled(mCCTVEnabled)

                    'If mExpiredrunning = 168 Then
                    '    mDBManager.AddExpiredRunningDuration(str1(1))
                    'Else
                    '    mDBManager.UpdateExpiredRunningDuration(str1(1))
                    'End If
                    'mExpiredrunning = str1(1)
            End Select
        Next

        mAntennaID = mDBManager.FindAntennaID(mTermInformation.TerminalNo)
        mAntennaNo = mDBManager.FindAntennaNo(mTermInformation.TerminalNo)

        initHardwareManager()

        Dim fatalError As Boolean = False
        If mHDwareMgr.SelfTest() = SelfTestResult.FATALREMERROR Then
            fatalError = True
        End If

        mHDwareMgr.DetectInterval = My.Settings.DetectInterval

        If fatalError = True Then
            'stop the system 'Implemented later
        Else
            'If HasEps() = True Then
            '    Dim obj As Object = mDBManager.FindCHUID(mTermInformation.TerminalNo)
            '    mCHUID = IIf(obj Is DBNull.Value, -1, obj)
            '    If obj Is DBNull.Value Then
            '        CType(Eps, EpsService).HasCHU = False
            '    Else
            '        CType(Eps, EpsService).HasCHU = True
            '    End If
            'End If
            SendSimpleCmdToServer(ComCodes.TransactionTypeSelections.CheckAllHardwareStatusOK, Parkrite.SystemShared.NetworkReferences.DeviceServiceDefinition.DeviceType.CMC_Device, mDeviceBone.SMCIPAddress)
        End If

        If fatalError = False Then
            Dim i As Integer
            For i = 0 To mServices.Count - 1
                Select Case CType(mServices(i), ITerminalService).selftest()
                    Case SelfTestResult.FATALCASHCARDERROR, SelfTestResult.FATALEPSERROR,
                            SelfTestResult.FATALREMERROR
                        fatalError = True
                End Select
            Next
        End If

        mAllTerminals = mDBManager.RetrieveAllTerminalsEx()
        For Each ter As Terminal In mAllTerminals
            If mTermInformation.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM Then
                If ter.DestinationSubCarpark = mTermInformation.DestinationSubCarpark Or ter.SourceSubCarpark = mTermInformation.DestinationSubCarpark Then
                    mTermsWithSameSubCP(ter.TerminalNo) = ter
                End If
            ElseIf mTermInformation.TerminalType = Enumeration.TERMINALOPTION.EXITTERM Then
                If ter.DestinationSubCarpark = mTermInformation.SourceSubCarpark Or ter.SourceSubCarpark = mTermInformation.SourceSubCarpark Then
                    mTermsWithSameSubCP(ter.TerminalNo) = ter
                End If
            End If
        Next

        ''mHDwareMgr.TerminalType = mTermInformation.TerminalType

        mDBManager.StartOpsThread()

        'Console.WriteLine("time1 : " & btm & " time2 : " & cccinterv)

    End Sub

    Private Sub GenerateServices(ByVal wmodes() As WorkingMode)
        mServices = New ArrayList(wmodes.Length - 1)
        Dim therow As WorkingMode
        Dim cnt As Integer
        For Each therow In wmodes
            Select Case therow.mode
                Case Enumeration.WORKINGMODEOPTION.FULLEPS
                    If mTermType = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device Then
                        Dim theservice As New EPSEntryService(mDeviceBone, Me)
                        theservice.ParkingType = therow.ParkingType
                        If theservice.ParkingType <> Enumeration.PARKINGTYPENUM.SEASON Then
                            If mTermInformation.SeasonOnly = True Then
                                theservice.ParkingType = Enumeration.PARKINGTYPENUM.SEASON
                            End If
                        End If
                        mServices.Insert(cnt, theservice)
                        mFullEpsIndex = cnt
                        AddHandler theservice.IdentityAuthenticateSucceed, AddressOf IdentityAuthenticateSucceedHandler
                    ElseIf mTermType = NetworkReferences.DeviceServiceDefinition.DeviceType.Exit_Device Then
                        Dim theservice As New EPSExitService(mDeviceBone, Me)
                        theservice.ParkingType = therow.ParkingType
                        If theservice.ParkingType <> Enumeration.PARKINGTYPENUM.SEASON Then
                            If mTermInformation.SeasonOnly = True Then
                                theservice.ParkingType = Enumeration.PARKINGTYPENUM.SEASON
                            End If
                        End If
                        mServices.Insert(cnt, theservice)
                        mFullEpsIndex = cnt
                        AddHandler theservice.IdentityAuthenticateSucceed, AddressOf IdentityAuthenticateSucceedHandler
                    Else
                        Throw New ApplicationException("Fatal Error: Wrong Terminal Type")
                    End If
                Case Enumeration.WORKINGMODEOPTION.SEMIEPS
                    'Create semieps service
                    'If mTermType = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device Then
                    '    Dim theservice As New SemiEPSEntryService(mDeviceBone, Me)
                    '    theservice.ParkingType = therow.ParkingType
                    '    If theservice.ParkingType <> Enumeration.PARKINGTYPENUM.SEASON Then
                    '        If mTermInformation.SeasonOnly = True Then
                    '            theservice.ParkingType = Enumeration.PARKINGTYPENUM.SEASON
                    '        End If
                    '    End If
                    '    mServices.Insert(cnt, theservice)
                    '    mSemiEpsIndex = cnt
                    '    AddHandler theservice.IdentityAuthenticateSucceed, AddressOf IdentityAuthenticateSucceedHandler
                    'ElseIf mTermType = NetworkReferences.DeviceServiceDefinition.DeviceType.Exit_Device Then
                    '    Dim theservice As New SemiEPSExitService(mDeviceBone, Me)
                    '    theservice.ParkingType = therow.ParkingType
                    '    If theservice.ParkingType <> Enumeration.PARKINGTYPENUM.SEASON Then
                    '        If mTermInformation.SeasonOnly = True Then
                    '            theservice.ParkingType = Enumeration.PARKINGTYPENUM.SEASON
                    '        End If
                    '    End If
                    '    mServices.Insert(cnt, theservice)
                    '    mSemiEpsIndex = cnt
                    '    AddHandler theservice.IdentityAuthenticateSucceed, AddressOf IdentityAuthenticateSucceedHandler
                    'Else
                    '    Throw New ApplicationException("Fatal Error: Wrong Terminal Type")
                    'End If
                Case Enumeration.WORKINGMODEOPTION.CASHCARD
                    'Create cashcard service
                    'If mTermType = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device Then
                    '    Dim theservice As New CashCardEntryService(mDeviceBone, Me)
                    '    theservice.ParkingType = therow.ParkingType
                    '    If theservice.ParkingType <> Enumeration.PARKINGTYPENUM.SEASON Then
                    '        If mTermInformation.SeasonOnly = True Then
                    '            theservice.ParkingType = Enumeration.PARKINGTYPENUM.SEASON
                    '        End If
                    '    End If
                    '    mServices.Insert(cnt, theservice)
                    '    mCashCardIndex = cnt
                    '    AddHandler theservice.IdentityAuthenticateSucceed, AddressOf IdentityAuthenticateSucceedHandler
                    'ElseIf mTermType = NetworkReferences.DeviceServiceDefinition.DeviceType.Exit_Device Then
                    '    Dim theservice As New CashCardExitService(mDeviceBone, Me)
                    '    theservice.ParkingType = therow.ParkingType
                    '    If theservice.ParkingType <> Enumeration.PARKINGTYPENUM.SEASON Then
                    '        If mTermInformation.SeasonOnly = True Then
                    '            theservice.ParkingType = Enumeration.PARKINGTYPENUM.SEASON
                    '        End If
                    '    End If
                    '    mServices.Insert(cnt, theservice)
                    '    mCashCardIndex = cnt
                    '    AddHandler theservice.IdentityAuthenticateSucceed, AddressOf IdentityAuthenticateSucceedHandler
                    'Else
                    '    Throw New ApplicationException("Fatal Error: Wrong Terminal Type")
                    'End If
                Case Enumeration.WORKINGMODEOPTION.REMTICKET
                    'Create REM service
                    'If mTermType = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device Then
                    '    Dim theservice As New RemEntryService(mDeviceBone, Me)
                    '    theservice.ParkingType = therow.ParkingType
                    '    If theservice.ParkingType <> Enumeration.PARKINGTYPENUM.SEASON Then
                    '        If mTermInformation.SeasonOnly = True Then
                    '            theservice.ParkingType = Enumeration.PARKINGTYPENUM.SEASON
                    '        End If
                    '    End If
                    '    mServices.Insert(cnt, theservice)
                    '    mREMIndex = cnt
                    '    AddHandler theservice.IdentityAuthenticateSucceed, AddressOf IdentityAuthenticateSucceedHandler
                    'ElseIf mTermType = NetworkReferences.DeviceServiceDefinition.DeviceType.Exit_Device Then
                    '    Dim theservice As New RemExitService(mDeviceBone, Me)
                    '    theservice.ParkingType = therow.ParkingType
                    '    If theservice.ParkingType <> Enumeration.PARKINGTYPENUM.SEASON Then
                    '        If mTermInformation.SeasonOnly = True Then
                    '            theservice.ParkingType = Enumeration.PARKINGTYPENUM.SEASON
                    '        End If
                    '    End If
                    '    mServices.Insert(cnt, theservice)
                    '    mREMIndex = cnt
                    '    AddHandler theservice.IdentityAuthenticateSucceed, AddressOf IdentityAuthenticateSucceedHandler
                    'Else
                    '    Throw New ApplicationException("Fatal Error: Wrong Terminal Type")
                    'End If
            End Select
            cnt += 1
        Next

        If mTermType = DeviceType.Entry_Device Then
            mDeviceBone.AddPublisher(MultiCastPort.Entry_Record1)
            mDeviceBone.AddSubscriber(MultiCastPort.Entry_Record1)
            mDeviceBone.AddSubscriber(MultiCastPort.Exit_Record1)
        Else
            mDeviceBone.AddPublisher(MultiCastPort.Exit_Record1)
            mDeviceBone.AddSubscriber(MultiCastPort.Entry_Record1)
            mDeviceBone.AddSubscriber(MultiCastPort.Exit_Record1)
        End If

        For Each sev As ITerminalService In mServices
            sev.init()
        Next
    End Sub

    Private Sub DoTheSyncronization(ByVal strs() As String)
        Try


            TerminalSite.TerminalLog.Log("started DoTheSyncronization ....")

            Dim cnt As Integer = 0
            For ii As Integer = 1 To strs.Length - 1

                'TerminalSite.TerminalLog.Log(strs(ii)) ''log

                Dim str1() As String = strs(ii).Split("|")

                Select Case CType(str1(0), TransactionTypeSelections)
                    Case ComCodes.TransactionTypeSelections.DownloadAllIUSeasonInfo
                        mDBManager.DoSeasonDBOps(String.Empty, DatabaseManager.DBAction.DELETE, DatabaseManager.DBSeasonDataType.IUSeason)
                        Eps.ClearSeason()
                        cnt = 0

                        Dim strln As Integer = str1.Length - 1

                        TerminalSite.TerminalLog.Log("DownloadAllIUSeasonInfo") ''log
                        TerminalSite.TerminalLog.Log("Sent Total Count =" & strln) ''log

                        For ia As Integer = 1 To strln
                            Dim str2() As String = str1(ia).Split(";")
                            Dim theinfo As New ClsSeasonInfo
                            theinfo.Ticket = str2(0)
                            theinfo.ValidFrom = DateTime.ParseExact(str2(1), "dd/MM/yyyy HH:mm:ss", Nothing)
                            theinfo.ValidTo = DateTime.ParseExact(str2(2), "dd/MM/yyyy HH:mm:ss", Nothing)
                            'strr = CType(msg.message5, String).Split(";"c)
                            If str2(3) <> String.Empty Then
                                theinfo.SeasonType = CType(str2(3), Short)
                            Else
                                theinfo.SeasonType = -1
                            End If
                            If str2(4) <> String.Empty Then
                                theinfo.GroupNo = CType(str2(4), Short)
                            Else
                                theinfo.GroupNo = -1
                            End If
                            theinfo.Freeze = CType(str2(5), Boolean)
                            theinfo.IOCheck = CType(str2(6), Boolean)
                            Eps.UpdateSeason(theinfo)
                            mDBManager.DoSeasonDBOps(theinfo, DatabaseManager.DBAction.UPDATE, DatabaseManager.DBSeasonDataType.IUSeason)

                            cnt = cnt + 1 'zaw
                        Next

                        TerminalSite.TerminalLog.Log("Updated Total Count =" & cnt) ''log

                        'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyIUSeasonCount, strln) 'zaw commented
                        ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyIUSeasonCount, cnt)

                    Case ComCodes.TransactionTypeSelections.DownloadCashCardSeasonInfo
                    'If IsNothing(CashCardSys) = False Then
                    '    mDBManager.DoSeasonDBOps(String.Empty, DatabaseManager.DBAction.DELETE, DatabaseManager.DBSeasonDataType.CashCardSeason)
                    '    CashCardSys.ClearSeason()
                    '    cnt = 0
                    '    For ia As Integer = 1 To str1.Length - 1
                    '        Dim str2() As String = str1(ia).Split(";")
                    '        Dim theinfo As New ClsSeasonInfo
                    '        theinfo.Ticket = str2(0)
                    '        theinfo.ValidFrom = DateTime.ParseExact(str2(1), "dd/MM/yyyy HH:mm:ss", Nothing)
                    '        theinfo.ValidTo = DateTime.ParseExact(str2(2), "dd/MM/yyyy HH:mm:ss", Nothing)

                    '        If str2(3) <> String.Empty Then
                    '            theinfo.SeasonType = CType(str2(3), Short)
                    '        Else
                    '            theinfo.SeasonType = -1
                    '        End If
                    '        If str2(4) <> String.Empty Then
                    '            theinfo.GroupNo = CType(str2(4), Short)
                    '        Else
                    '            theinfo.GroupNo = -1
                    '        End If
                    '        theinfo.Freeze = CType(str2(5), Boolean)
                    '        theinfo.IOCheck = CType(str2(6), Boolean)
                    '        CashCardSys.UpdateSeason(theinfo)
                    '        mDBManager.DoSeasonDBOps(theinfo, DatabaseManager.DBAction.UPDATE, DatabaseManager.DBSeasonDataType.CashCardSeason)
                    '    Next
                    'End If
                    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyCashCardSeasonCount, str1.Length - 1)

                    Case ComCodes.TransactionTypeSelections.DownloadCashCardSeasonPermits
                    'If IsNothing(CashCardSys) = False Then
                    '    mDBManager.DoSeasonDBOps(-1, DatabaseManager.DBAction.DELETE, DatabaseManager.DBSeasonDataType.CCSeasonPermit)
                    '    'mDBManager.DeleteAllCashCardSeasonPermits()
                    '    cnt = 0
                    '    For ia As Integer = 1 To str1.Length - 1
                    '        Dim str2() As String = str1(ia).Split(";")
                    '        Dim theinfo As New ClsSeasonPermits
                    '        theinfo.Id = CType(str2(0), Integer)
                    '        theinfo.Ticket = str2(1)
                    '        theinfo.CarparkNo = CType(str2(2), Short)
                    '        theinfo.SubCarparkNo = CType(str2(3), Short)
                    '        CashCardSys.UpdateSeasonPermits(theinfo)
                    '        mDBManager.DoSeasonDBOps(theinfo, DatabaseManager.DBAction.UPDATE, DatabaseManager.DBSeasonDataType.CCSeasonPermit)
                    '    Next
                    'End If

                    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyCashCardSeasonPermitsCount, str1.Length - 1)

                    Case ComCodes.TransactionTypeSelections.DownloadIUSeasonPermits
                        mDBManager.DoSeasonDBOps(-1, DatabaseManager.DBAction.DELETE, DatabaseManager.DBSeasonDataType.IUSeasonPermit)
                        'mDBManager.DeleteAllCashCardSeasonPermits()
                        cnt = 0
                        For ia As Integer = 1 To str1.Length - 1
                            Dim str2() As String = str1(ia).Split(";")
                            Dim theinfo As New ClsSeasonPermits
                            theinfo.Id = CType(str2(0), Integer)
                            theinfo.Ticket = str2(1)
                            theinfo.CarparkNo = CType(str2(2), Short)
                            theinfo.SubCarparkNo = CType(str2(3), Short)
                            Eps.UpdateSeasonPermits(theinfo)
                            mDBManager.DoSeasonDBOps(theinfo, DatabaseManager.DBAction.UPDATE, DatabaseManager.DBSeasonDataType.IUSeasonPermit)
                        Next

                        ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyIUSeasonPermitsCount, str1.Length - 1)

                    Case ComCodes.TransactionTypeSelections.DownloadSeasonGroup
                        mDBManager.DoSeasonDBOps(-1, DatabaseManager.DBAction.DELETE, DatabaseManager.DBSeasonDataType.SeasonGroup)
                        ClearGroupSeasonInfo()
                        cnt = 0
                        For ia As Integer = 1 To str1.Length - 1
                            Dim str2() As String = str1(ia).Split(";")
                            Dim theinfor As New ClsSeasonGroup
                            theinfor.GroupNo = str2(0)
                            theinfor.Maximum = str2(1)
                            theinfor.Threshold = str2(2)
                            theinfor.CurrentCount = str2(3)
                            UpdateGroupSeasonInfo(theinfor)
                            mDBManager.DoSeasonDBOps(theinfor, DatabaseManager.DBAction.UPDATE, DatabaseManager.DBSeasonDataType.SeasonGroup)
                        Next

                        ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplySeasonGroupCount, str1.Length - 1)

                    Case ComCodes.TransactionTypeSelections.DownloadGroupSeasonPermits
                        mDBManager.DoSeasonDBOps(-1, DatabaseManager.DBAction.DELETE, DatabaseManager.DBSeasonDataType.SeasonGroupPermit)
                        'mDBManager.DeleteAllCashCardSeasonPermits()
                        cnt = 0
                        For ia As Integer = 1 To str1.Length - 1
                            Dim str2() As String = str1(ia).Split(";")
                            Dim theinfo As New GroupSeasonPermits
                            theinfo.Id = CType(str2(0), Integer)
                            theinfo.GroupNo = str2(1)
                            theinfo.CarparkNo = CType(str2(2), Short)
                            theinfo.SubCarparkNo = CType(str2(3), Short)
                            UpdateGroupSeasonPermits(theinfo)
                            mDBManager.DoSeasonDBOps(theinfo, DatabaseManager.DBAction.UPDATE, DatabaseManager.DBSeasonDataType.SeasonGroupPermit)
                        Next

                        ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyGroupSeasonPermitsCount, str1.Length - 1)

                End Select
            Next

        Catch ex As Exception
            TerminalSite.ErrorLog.Log("Error in  DoTheSyncronization =" & ex.ToString()) ''log
        End Try

    End Sub

    Public Sub SyncRunningRec(ByVal strs() As String)
        Dim cnt As Integer = 0
        For ii As Integer = 1 To strs.Length - 1
            Dim str1() As String = strs(ii).Split("|")
            Select Case CType(str1(0), TransactionTypeSelections)
                Case ComCodes.TransactionTypeSelections.DownloadRunningHourly
                    'mDBManager.DeleteAllRunningHourly()
                    'mRunningHourlyRecords.Clear() 'add on 16/10/08
                    'cnt = 0
                    'Dim tbtmp As New DataTable
                    'Dim i As Integer
                    'For i = 0 To 5
                    '    tbtmp.Columns.Add()
                    'Next
                    'For ia As Integer = 1 To str1.Length - 1
                    '    Dim str2() As String = str1(ia).Split(";")
                    '    Dim rw As DataRow = tbtmp.NewRow
                    '    rw(1) = str2(0)
                    '    rw(2) = DateTime.ParseExact(str2(1), "dd/MM/yyyy HH:mm:ss", Nothing)
                    '    rw(3) = str2(2)
                    '    rw(4) = str2(3)
                    '    rw(5) = str2(4)
                    '    tbtmp.Rows.Add(rw)
                    '    Dim tmphor As New RunningHourly
                    '    tmphor.HourlyNo = str2(0)
                    '    tmphor.EntryTime = DateTime.ParseExact(str2(1), "dd/MM/yyyy HH:mm:ss", Nothing)
                    '    tmphor.TerminalNo = str2(2)
                    '    tmphor.HourlyType = str2(3)
                    '    tmphor.Status = str2(4)
                    '    'mRunningHourlyRecords(tmphor.HourlyNo) = tmphor
                    '    UpdateRunning(tmphor)
                    'Next
                    'cnt = mDBManager.AddRunningHourlyRecordsEx(tbtmp)
                    ''If cnt <> 0 Then
                    'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyRunningHourlyCount, cnt)
                    ''End If
                Case ComCodes.TransactionTypeSelections.DownloadRunningSeason
                    mDBManager.DeleteAllRunningSeason()
                    mRunningSeasonRecords.Clear()   'add on 16/10/08
                    cnt = 0
                    Dim tbtmp As New DataTable
                    Dim i As Integer
                    For i = 0 To 6
                        tbtmp.Columns.Add()
                    Next
                    tbtmp.Columns(2).DataType = GetType(DateTime)
                    For ia As Integer = 1 To str1.Length - 1
                        Dim str2() As String = str1(ia).Split(";")
                        Dim rw As DataRow = tbtmp.NewRow
                        rw(1) = str2(0)
                        rw(2) = DateTime.ParseExact(str2(1), "dd/MM/yyyy HH:mm:ss", Nothing)
                        rw(3) = str2(2)
                        rw(4) = str2(3)
                        rw(5) = IIf(str2(4) = "", 0, str2(4))
                        rw(6) = str2(5)
                        tbtmp.Rows.Add(rw)
                        Dim tmphor As New RunningSeason
                        tmphor.SeasonNo = str2(0)
                        tmphor.EntryTime = DateTime.ParseExact(str2(1), "dd/MM/yyyy HH:mm:ss", Nothing)
                        tmphor.TerminalNo = str2(2)
                        tmphor.SeasonOption = str2(3)
                        tmphor.SeasonType = IIf(str2(4) = "", 0, str2(4))
                        tmphor.Status = str2(5)
                        'mRunningSeasonRecords(tmphor.SeasonNo) = tmphor
                        UpdateRunning(tmphor)
                    Next
                    cnt = mDBManager.AddRunningSeasonRecordsEx(tbtmp)
                    'If cnt <> 0 Then
                    ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyRunningSeasonCount, cnt)
                    'End If
            End Select
        Next
    End Sub

    Private Sub HouseKeepproc(ByVal obj As Object)

        mHouseKeepingTimer.Change(Threading.Timeout.Infinite, Threading.Timeout.Infinite)

        If Me.mSysMode = Enumeration.SystemMode.ACTIVE Then
            Dim keystodelete As New ArrayList
            'SyncLock mRecordNotFounds.SyncRoot
            '    For Each key As String In mRecordNotFounds.Keys
            '        Dim info As RecordNotFoundInfo = mRecordNotFounds(key)
            '        If Now.Subtract(info.TheTime).TotalMinutes >= 10 Then
            '            keystodelete.Add(key)
            '        End If
            '    Next
            '    For Each key As String In keystodelete
            '        mRecordNotFounds.Remove(key)
            '    Next
            'End SyncLock

            'keystodelete.Clear()
            SyncLock mEntitiesProcessed.SyncRoot
                For Each key As String In mEntitiesProcessed.Keys
                    Dim info As DateTime = mEntitiesProcessed(key)
                    If Now.Subtract(info).TotalMinutes >= 1 Then ''zaw change to 1 minutes ''25/08/2016
                        keystodelete.Add(key)
                    End If
                Next
                For Each key As String In keystodelete
                    mEntitiesProcessed.Remove(key)
                Next
            End SyncLock


            keystodelete.Clear()
            SyncLock mEntitiesProcessing.SyncRoot
                For Each key As String In mEntitiesProcessing.Keys
                    Dim info As EntityProcessing = mEntitiesProcessing(key)
                    'If Now.Subtract(info.timelabel).TotalMinutes >= 10 Then ''zaw change to 1 minutes ''25/08/2016
                    If Now.Subtract(info.timelabel).TotalMinutes >= 1 Then
                        keystodelete.Add(key)
                    End If
                Next
                For Each key As String In keystodelete
                    mEntitiesProcessing.Remove(key)
                Next
            End SyncLock

            'keystodelete.Clear()
            'SyncLock mReceiptHash.SyncRoot
            '    For Each key As String In mReceiptHash.Keys
            '        Dim info As ReceiptInfo = mReceiptHash(key)
            '        If Now.Subtract(info.PaidTime).TotalMinutes >= 10 Then
            '            keystodelete.Add(key)
            '        End If
            '    Next
            '    For Each key As String In keystodelete
            '        mReceiptHash.Remove(key)
            '    Next
            'End SyncLock

            'If Now.Minute Mod 30 = 0 Then
            '    mDBManager.DoSeasonDBOps(Now, DatabaseManager.DBAction.DELETEBYTIME, DatabaseManager.DBSeasonDataType.Redemption)
            'End If
            'keystodelete.Clear()
            'SyncLock Me.mRedemptionHash.SyncRoot
            '    For Each key As String In mRedemptionHash.Keys
            '        Dim info As Redemption = mRedemptionHash(key)
            '        If Now > info.ValidTo Then
            '            keystodelete.Add(key)
            '        End If
            '    Next
            '    For Each key As String In keystodelete
            '        mRedemptionHash.Remove(key)
            '    Next
            'End SyncLock

            'If Now.Minute Mod 30 = 0 Then
            '    mDBManager.DoSeasonDBOps(Now, DatabaseManager.DBAction.DELETEBYTIME, DatabaseManager.DBSeasonDataType.Complementary)
            'End If
            'keystodelete.Clear()
            'SyncLock Me.mComplimentaryHash.SyncRoot
            '    For Each key As String In mComplimentaryHash.Keys
            '        Dim info As Complimentary = mComplimentaryHash(key)
            '        If Now > info.ValidTo Then
            '            keystodelete.Add(key)
            '        End If
            '    Next
            '    For Each key As String In keystodelete
            '        mComplimentaryHash.Remove(key)
            '    Next
            'End SyncLock
        End If
        'mHouseKeepingTimer.Change(6000, 6000)
        mHouseKeepingTimer.Change(15000, 15000) ''zaw changed to 15 seconds '25/08/2016', not require too frequent checking.
    End Sub

    Private Sub UpdateGroupSeasonDetails(ByVal strs() As String)
        For ii As Integer = 1 To strs.Length - 1
            Dim str1() As String = strs(ii).Split("|")
            Select Case CType(str1(0), TransactionTypeSelections)
                Case ComCodes.TransactionTypeSelections.UpdateGroupSeasonCount
                    'Dim str2() As String = str1(1).Split(";")
                    'Dim grpno As Integer = str2(0)
                Case ComCodes.TransactionTypeSelections.DownloadRunningSeason
                    For ia As Integer = 1 To str1.Length - 1
                        Dim str2() As String = str1(ia).Split(";")
                        Dim tmprunning As New RunningSeason
                        tmprunning.SeasonNo = str2(0)
                        tmprunning.EntryTime = DateTime.ParseExact(str2(1), "dd/MM/yyyy HH:mm:ss", Nothing)
                        tmprunning.TerminalNo = str2(2)
                        tmprunning.SeasonOption = CType(str2(3), Enumeration.SEASONTOPTIONS)
                        tmprunning.SeasonType = IIf(str2(4) = String.Empty, -1, str2(4))
                        tmprunning.Status = str2(5)
                        If IsTerminalInsideThisCarpark(tmprunning.TerminalNo) Then
                            UpdateRunning(tmprunning)
                            mDBManager.DoRunningSeasonRecord(tmprunning, DatabaseManager.DBAction.UPDATE)
                        End If
                    Next
                Case ComCodes.TransactionTypeSelections.DownloadRunningHourly
                    'For ia As Integer = 1 To str1.Length - 1
                    '    Dim str2() As String = str1(ia).Split(";")
                    '    Dim tmprunning As New RunningHourly
                    '    tmprunning.HourlyNo = str2(0)
                    '    tmprunning.EntryTime = DateTime.ParseExact(str2(1), "dd/MM/yyyy HH:mm:ss", Nothing)
                    '    tmprunning.TerminalNo = str2(2)
                    '    tmprunning.HourlyType = CType(str2(3), Enumeration.HOURLYTYPEOPTION)
                    '    tmprunning.Status = str2(4)
                    '    If IsTerminalInsideThisCarpark(tmprunning.TerminalNo) Then
                    '        UpdateRunning(tmprunning)
                    '        mDBManager.DoRunningHourRecord(tmprunning, DatabaseManager.DBAction.UPDATE)
                    '    End If
                    'Next
                Case ComCodes.TransactionTypeSelections.DownloadGroupMembers
                    Dim str2() As String = str1(1).Split(";")
                    For Each thestr As String In str2
                        'SyncLock mRunningHourlyRecords.SyncRoot
                        '    If mRunningHourlyRecords.Contains(thestr) Then
                        '        mRunningHourlyRecords.Remove(thestr)
                        '    End If
                        'End SyncLock

                        SyncLock mRunningSeasonRecords.SyncRoot
                            If mRunningSeasonRecords.Contains(thestr) Then
                                mRunningSeasonRecords.Remove(thestr)
                            End If
                        End SyncLock

                        'mDBManager.DoRunningHourRecord(thestr, DatabaseManager.DBAction.DELETE)
                        mDBManager.DoRunningSeasonRecord(thestr, DatabaseManager.DBAction.DELETE)
                    Next
            End Select
        Next
    End Sub

    Private Sub ProcessMsgforLocal(ByVal msg As TransactionMsg)
        Select Case msg.TransactionType
            Case ComCodes.TransactionTypeSelections.DownloadSubCarparkInfo
                Dim tbtmp As New DataTable
                Dim i As Integer
                For i = 0 To 6
                    tbtmp.Columns.Add()
                Next
                Dim rw As DataRow = tbtmp.NewRow
                Dim strr() As String = CType(msg.message1, String).Split(";"c)
                rw(0) = strr(0)
                rw(6) = strr(1)
                rw(1) = msg.message2
                rw(2) = msg.message3
                rw(3) = msg.message4
                rw(4) = msg.message5
                rw(5) = Integer.Parse(CType(msg.message6, String))
                tbtmp.Rows.Add(rw)
                If mDBManager.AddSubCarparkRecords(tbtmp) <> 1 Then
                    Dim str As String = String.Format("Type: {0}, Subcarpark No: {1}, Carpark No: {2}", msg.TransactionType, rw(0), rw(6))
                    replyDownloadMsgError(ComCodes.TransactionTypeSelections.DownloadSubCarparkInfo, rw(0), str)
                Else
                    'Dim funct1 As New RetrieveFromDB(AddressOf mDBManager.RetrieveAllSubCarparks)
                    'VerifyIfFinished(ComCodes.TransactionTypeSelections.DownloadSubCarparkInfo, ComCodes.TransactionTypeSelections.ReplySubCarparkCount, funct1)
                End If
            Case ComCodes.TransactionTypeSelections.DownloadTerminalInfo
                Dim tbtmp As New DataTable
                Dim i As Integer
                For i = 0 To 8
                    tbtmp.Columns.Add()
                Next
                Dim rw As DataRow = tbtmp.NewRow
                rw(0) = msg.message1
                rw(1) = msg.message2
                Dim strr() As String = CType(msg.message3, String).Split(";"c)
                rw(2) = strr(0)
                rw(5) = strr(1)
                strr = CType(msg.message4, String).Split(";"c)
                rw(3) = strr(0)
                rw(4) = strr(1)
                strr = CType(msg.message5, String).Split(";"c)
                rw(6) = strr(0)
                rw(7) = msg.message6
                rw(8) = IIf(strr(1) = String.Empty, -1, strr(1))
                tbtmp.Rows.Add(rw)
                If mDBManager.AddTerminalRecords(tbtmp) <> 1 Then
                    Dim str As String = String.Format("Type: {0}, Terminal No: {1}, SourceSubCarpark No: {2}, CarparkNo: {3}",
                    msg.TransactionType, rw(0), rw(3), rw(4))
                    replyDownloadMsgError(msg.TransactionType, rw(0), str)
                Else
                    If rw(6) = mLocalIPaddress Then
                        mTermInformation.CarparkNo = rw(5)
                        If Not rw(7) Is DBNull.Value Then
                            mTermInformation.Description = rw(7)
                        End If
                        mTermInformation.IPAddress = rw(6)
                        mTermInformation.SourceSubCarpark = CType(rw(3), Short)
                        mTermInformation.DestinationSubCarpark = CType(rw(4), Short)
                        mTermInformation.TerminalName = rw(1)
                        mTermInformation.TerminalNo = rw(0)
                        mTermInformation.TerminalType = rw(2)
                        mTermInformation.RateSetNo = rw(8)
                        Select Case mTermInformation.TerminalType
                            Case Enumeration.TERMINALOPTION.ENTRYTERM
                                mTermType = NetworkReferences.DeviceServiceDefinition.DeviceType.Entry_Device
                                mDeviceBone.IgnoreHPC = True
                            Case Enumeration.TERMINALOPTION.EXITTERM
                                mTermType = NetworkReferences.DeviceServiceDefinition.DeviceType.Exit_Device
                            Case Enumeration.TERMINALOPTION.HPC
                                mTermType = NetworkReferences.DeviceServiceDefinition.DeviceType.HPC_Device
                        End Select
                    End If
                    'Dim funct1 As New RetrieveFromDB(AddressOf mDBManager.RetrieveAllTerminals)
                    'VerifyIfFinished(ComCodes.TransactionTypeSelections.DownloadTerminalInfo, ComCodes.TransactionTypeSelections.ReplyTerminalCount, funct1)
                End If
            Case ComCodes.TransactionTypeSelections.DownloadTerminalRelation
                Dim tbtmp As New DataTable
                Dim i As Integer
                For i = 0 To 4
                    tbtmp.Columns.Add()
                Next
                Dim rw As DataRow = tbtmp.NewRow
                rw(0) = msg.message1
                rw(1) = msg.message2
                rw(2) = msg.message3
                rw(3) = msg.message4
                rw(4) = msg.message5
                tbtmp.Rows.Add(rw)
                If mDBManager.AddTerminalRelationRecords(tbtmp) <> 1 Then
                    Dim str As String = String.Format("Type: {0}, ID: {1}", msg.TransactionType, rw(0))
                    replyDownloadMsgError(msg.TransactionType, rw(0), str)
                Else
                    'Dim funct1 As New RetrieveFromDB(AddressOf mDBManager.RetrieveAllTerminalRelations)
                    'VerifyIfFinished(ComCodes.TransactionTypeSelections.DownloadTerminalRelation, ComCodes.TransactionTypeSelections.ReplyTerminalRelationCount, funct1)
                End If
            Case ComCodes.TransactionTypeSelections.DownloadWorkingMode
                Dim tbtmp As New DataTable
                Dim i As Integer
                For i = 0 To 5
                    tbtmp.Columns.Add()
                Next
                Dim rw As DataRow = tbtmp.NewRow
                For i = 0 To 5
                    rw(i) = msg.message(i)
                Next
                tbtmp.Rows.Add(rw)
                If mDBManager.AddWorkingModes(tbtmp) <> 1 Then
                    Dim str As String = String.Format("Type: {0}, WorkingMode: {1}", msg.TransactionType, rw(0))
                    replyDownloadMsgError(msg.TransactionType, rw(0), str)
                Else
                    'Dim funct1 As New RetrieveFromDB(AddressOf mDBManager.RetrieveAllWorkingModes)
                    'VerifyIfFinished(ComCodes.TransactionTypeSelections.DownloadWorkingMode, ComCodes.TransactionTypeSelections.ReplyWorkingModeCount, funct1)
                End If
            Case ComCodes.TransactionTypeSelections.DownloadComplimentary
                'modifyComplimentaryDB(msg, ComCodes.TransactionTypeSelections.DownloadComplimentary)
            Case ComCodes.TransactionTypeSelections.UpdateComplimentary
                'modifyComplimentaryDB(msg, ComCodes.TransactionTypeSelections.UpdateComplimentary)
            Case ComCodes.TransactionTypeSelections.DownloadRedemption
                'modifyRedemptionDB(msg, ComCodes.TransactionTypeSelections.DownloadRedemption)
            Case ComCodes.TransactionTypeSelections.UpdateRedemption
                'modifyRedemptionDB(msg, ComCodes.TransactionTypeSelections.UpdateRedemption)
            Case ComCodes.TransactionTypeSelections.DownloadGroupSeasonPermits
                modifyseasonGroupPermitsDB(msg, ComCodes.TransactionTypeSelections.DownloadGroupSeasonPermits)
            Case ComCodes.TransactionTypeSelections.UpdateGroupSeasonPermits
                modifyseasonGroupPermitsDB(msg, ComCodes.TransactionTypeSelections.UpdateGroupSeasonPermits)
            Case ComCodes.TransactionTypeSelections.DownloadSeasonGroup
                modifyseasonGroupDB(msg, ComCodes.TransactionTypeSelections.DownloadSeasonGroup)
            Case ComCodes.TransactionTypeSelections.UpdateSeasonGroup
                modifyseasonGroupDB(msg, ComCodes.TransactionTypeSelections.UpdateSeasonGroup)
            Case ComCodes.TransactionTypeSelections.DownloadHoliday
                'modifyHolidayDB(msg, ComCodes.TransactionTypeSelections.DownloadHoliday)
            Case ComCodes.TransactionTypeSelections.UpdateHoliday
                'modifyHolidayDB(msg, ComCodes.TransactionTypeSelections.UpdateHoliday)
            Case ComCodes.TransactionTypeSelections.DownloadRateSet
                'Dim tbtmp As New DataTable
                'Dim i As Integer
                'For i = 0 To 7
                '    tbtmp.Columns.Add()
                'Next
                'Dim rw As DataRow = tbtmp.NewRow
                'rw(0) = msg.message1
                'Dim strr() As String = CType(msg.message2, String).Split(";"c)
                'rw(1) = CType(strr(0), Integer)
                'rw(2) = CType(strr(1), Integer)
                'rw(3) = CType(strr(2), Integer)
                'rw(4) = CType(strr(3), Integer)
                'rw(5) = CType(strr(4), Integer)
                'rw(6) = CType(strr(5), Integer)
                'rw(7) = CType(strr(6), Integer)
                'tbtmp.Rows.Add(rw)
                'If mDBManager.AddRateSetRecords(tbtmp) <> 1 Then
                '    Dim str As String = String.Format("Type: {0}, RateSet No: {1}", _
                '    msg.TransactionType, rw(0))
                '    replyDownloadMsgError(msg.TransactionType, rw(0), str)
                'Else
                '    Dim funct1 As New RetrieveFromDB(AddressOf mDBManager.RetrieveAllRateSet)
                '    VerifyIfFinished(ComCodes.TransactionTypeSelections.DownloadRateSet, ComCodes.TransactionTypeSelections.ReplyRateSetCount, funct1)
                'End If
            Case ComCodes.TransactionTypeSelections.ClearAllSeasonGroup
                If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                    mDBManager.DeleteAllSeasonGroup()
                End If
            Case ComCodes.TransactionTypeSelections.ClearAllGroupSeasonPermits
                If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                    mDBManager.DeleteAllGroupSeasonPermits()
                End If
            Case ComCodes.TransactionTypeSelections.ClearAllComplimentary
                'If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                '    mDBManager.DeleteAllComplimentary()
                'End If
            Case ComCodes.TransactionTypeSelections.ClearAllRedemption
                'If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                '    mDBManager.DeleteAllRedemption()
                'End If
            Case ComCodes.TransactionTypeSelections.ClearAllWorkingMode
                If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                    mDBManager.DeleteAllWorkingModes()
                End If
            Case ComCodes.TransactionTypeSelections.ClearAllSubCarparkInfo
                If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                    mDBManager.DeleteAllSubCarparks()
                End If
            Case ComCodes.TransactionTypeSelections.ClearAllTerminalInfo
                If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                    mDBManager.DeleteAllTerminals()
                End If
            Case ComCodes.TransactionTypeSelections.ClearAllRunningHourly
                'If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                '    mDBManager.DeleteAllRunningHourly()
                '    mRunningHourlyRecords.Clear()   'add on 16/10/08
                '    Dim cnt As Integer
                '    cnt = mDBManager.RetrieveAllRunningHourlyEx.Length
                '    ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyRunningHourlyCount, cnt)
                'End If
            Case ComCodes.TransactionTypeSelections.ClearAllRunningSeason
                'If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                mDBManager.DeleteAllRunningSeason()
                mRunningSeasonRecords.Clear() 'add on 16/10/08
                Dim cnt As Integer
                cnt = mDBManager.RetrieveAllRunningSeasonEx.Length
                ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyRunningSeasonCount, cnt)
                'End If
            Case ComCodes.TransactionTypeSelections.ClearAllTerminalRelation
                If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                    mDBManager.DeleteAllTerminalRelations()
                End If
            Case ComCodes.TransactionTypeSelections.SetTerminalActive
                'mDeviceBone.DeviceNature.MyDeviceType = mTermType
                'SetupHoliday()
                'SetupRate()
                'SetupRateSet()

                If mDBManager.GetMainConfigInfo(mTermInformation.CarparkNo, mMainConfig) <> True Then
                    'report error
                End If

                If mDBManager.GetAddonOptionInfo(mAddonOption) = False Then
                    Throw New ApplicationException("Cannot get Addon option information")
                Else
                    If mTermInformation.SourceSubCarpark = -1 Or mTermInformation.DestinationSubCarpark = -1 Then
                        mHDwareMgr.LEDEnabled = mAddonOption.LEDEnabled
                    ElseIf mTermInformation.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM Then
                        mHDwareMgr.LEDEnabled = mAddonOption.LEDEnabled
                    Else
                        mHDwareMgr.LEDEnabled = False
                    End If
                    mHDwareMgr.ShutterEnabled = mAddonOption.ShutterEnabled

                    'mHDwareMgr.TerminalType = mTermInformation.TerminalType
                    If mHDwareMgr.LEDEnabled = True Then
                        If HDManager.CreateLED = False Then
                            Dim transa As New TransactionMsg
                            transa.mLog = False
                            transa.TransactionType = ComCodes.TransactionTypeSelections.CEHardWareSelfTestError
                            SendMsgToSMC(transa)
                        End If
                    End If

                    If mAddonOption.SubCPWithinSubCP = True Then
                        mRelatedFromTerminal = mDBManager.FindRelatedFromTerminal(mTermInformation.TerminalNo)
                        If Not mRelatedFromTerminal Is Nothing Then
                            mIsToTerminal = True
                        End If
                        mRelatedToTerminal = mDBManager.FindRelatedToTerminal(mTermInformation.TerminalNo)
                        If Not mRelatedToTerminal Is Nothing Then
                            mIsFromTerminal = True
                        End If
                    End If
                End If
                mPGSEnabled = mDBManager.PGSIsEnabled()
                If mPGSEnabled = True Then
                    If mDBManager.GetPGSInfo(mTermInformation.TerminalNo, mPGS) = False Then
                        mPGS.TerminalNo = mTermInformation.TerminalNo
                        mPGS.Connected = False
                        mPGS.InformedBy = -1
                    End If
                End If

                'mDBManager.ResetFullStatus()

                mSysMode = Enumeration.SystemMode.ACTIVE
                Dim trans As New TransactionMsg
                trans.TransactionType = ComCodes.TransactionTypeSelections.SetTerminalActiveSucceed
                trans.mLog = False
                SendMsgToSMC(trans)
            Case ComCodes.TransactionTypeSelections.SetTerminalStandby
                mSysMode = Enumeration.SystemMode.STANDBY
                Dim trans As New TransactionMsg
                trans.TransactionType = ComCodes.TransactionTypeSelections.SetTerminalStandbySucceed
                SendMsgToSMC(trans)
            Case ComCodes.TransactionTypeSelections.DownloadCyclingBlockList
                'Dim tbtmp As New DataTable
                'Dim i As Integer
                'For i = 0 To 9
                '    tbtmp.Columns.Add()
                'Next
                'Dim rw As DataRow = tbtmp.NewRow
                'Dim strr() As String = CType(msg.message1, String).Split(";"c)
                'rw(0) = CType(strr(0), Integer)
                'rw(1) = CType(strr(1), Integer)
                'strr = CType(msg.message2, String).Split(";"c)
                'rw(2) = strr(0)
                'rw(3) = strr(1)
                'strr = CType(msg.message3, String).Split(";"c)
                'rw(4) = CType(strr(0), Boolean)
                'rw(5) = CType(strr(1), Single)
                'rw(6) = CType(strr(2), Single)
                'rw(7) = CType(strr(3), Integer)
                'rw(8) = CType(strr(4), Integer)
                'rw(9) = CType(strr(5), Integer)
                'tbtmp.Rows.Add(rw)
                ''Add to database
                'If mDBManager.AddCyclingBlockList(tbtmp) <> 1 Then
                '    Dim str As String = String.Format("Type: {0}, Block ID: {1}, Block Type: {2}, Rate ID: {3}", _
                '                        msg.TransactionType, rw(0), rw(1), rw(14))
                '    replyDownloadMsgError(msg.TransactionType, rw(0), str)
                'Else
                '    Dim funct1 As New RetrieveFromDB(AddressOf mDBManager.RetrieveAllCyclingBlockList)
                '    VerifyIfFinished(ComCodes.TransactionTypeSelections.DownloadCyclingBlockList, ComCodes.TransactionTypeSelections.ReplyCyclingBlockListCount, funct1)
                'End If
            Case ComCodes.TransactionTypeSelections.DownloadDiscreteBlockList
                'Dim tbtmp As New DataTable
                'Dim i As Integer
                'For i = 0 To 9
                '    tbtmp.Columns.Add()
                'Next
                'Dim rw As DataRow = tbtmp.NewRow
                'Dim strr() As String = CType(msg.message1, String).Split(";"c)
                'rw(0) = CType(strr(0), Integer)
                'rw(1) = CType(strr(1), Integer)
                'strr = CType(msg.message2, String).Split(";"c)
                'rw(2) = strr(0)
                'rw(3) = strr(1)
                'strr = CType(msg.message3, String).Split(";"c)
                'rw(4) = CType(strr(0), Boolean)
                'rw(5) = CType(strr(1), Single)
                'rw(6) = CType(strr(2), Single)
                'rw(7) = CType(strr(3), Integer)
                'rw(8) = CType(strr(4), Integer)
                'rw(9) = CType(strr(5), Integer)
                'tbtmp.Rows.Add(rw)
                ''Add to database
                'If mDBManager.AddDiscreteBlockList(tbtmp) <> 1 Then
                '    Dim str As String = String.Format("Type: {0}, Block ID: {1}, Block Type: {2}, Rate ID: {3}", _
                '                        msg.TransactionType, rw(0), rw(1), rw(14))
                '    replyDownloadMsgError(msg.TransactionType, rw(0), str)
                'Else
                '    Dim funct1 As New RetrieveFromDB(AddressOf mDBManager.RetrieveAllDiscreteBlockList)
                '    VerifyIfFinished(ComCodes.TransactionTypeSelections.DownloadDiscreteBlockList, ComCodes.TransactionTypeSelections.ReplyAllDiscreteBlockListCount, funct1)
                'End If
            Case ComCodes.TransactionTypeSelections.DownloadAdvancedBlockList
                'Dim tbtmp As New DataTable
                'Dim i As Integer
                'For i = 0 To 5
                '    tbtmp.Columns.Add()
                'Next
                'Dim rw As DataRow = tbtmp.NewRow
                'For i = 0 To 5
                '    rw(i) = msg.message(i)
                'Next
                'tbtmp.Rows.Add(rw)
                ''Add to database
                'If mDBManager.AddAdvancedBlockList(tbtmp) <> 1 Then
                '    Dim str As String = String.Format("Type: {0}, ID: {1}, Block ID: {2}", _
                '                        msg.TransactionType, rw(0), rw(5))
                '    replyDownloadMsgError(msg.TransactionType, rw(0), str)
                'Else
                '    Dim funct1 As New RetrieveFromDB(AddressOf mDBManager.RetrieveAllAdvancedBlockList)
                '    VerifyIfFinished(ComCodes.TransactionTypeSelections.DownloadAdvancedBlockList, ComCodes.TransactionTypeSelections.ReplyAdvancedBlockListCount, funct1)
                'End If
            Case ComCodes.TransactionTypeSelections.DownloadRateComposite
                'Dim tbtmp As New DataTable
                'Dim i As Integer
                'For i = 0 To 2
                '    tbtmp.Columns.Add()
                'Next
                'Dim rw As DataRow = tbtmp.NewRow
                'rw(0) = CType(msg.message1, Integer)
                'rw(1) = CType(msg.message2, Boolean)
                'rw(2) = CType(msg.message3, String)
                'tbtmp.Rows.Add(rw)
                ''Add to database
                'If mDBManager.AddRateComposite(tbtmp) <> 1 Then
                '    Dim str As String = String.Format("Type: {0}, RateComposite ID: {1}", _
                '    msg.TransactionType, rw(0))
                '    replyDownloadMsgError(msg.TransactionType, rw(0), str)
                'Else
                '    Dim funct1 As New RetrieveFromDB(AddressOf mDBManager.RetrieveAllRateComposite)
                '    VerifyIfFinished(ComCodes.TransactionTypeSelections.DownloadRateComposite, ComCodes.TransactionTypeSelections.ReplyRateCompositeCount, funct1)
                'End If
            Case ComCodes.TransactionTypeSelections.DownloadMainCarparkConfig
                Dim tbtmp As New DataTable
                Dim i As Integer
                For i = 0 To 6
                    tbtmp.Columns.Add()
                Next
                Dim rw As DataRow = tbtmp.NewRow
                rw(0) = CType(msg.message1, Short)
                rw(1) = CType(msg.message2, String)
                Dim strr() As String = CType(msg.message3, String).Split(";"c)
                rw(2) = CType(strr(0), Single)
                rw(3) = CType(strr(1), Single)
                rw(4) = CType(strr(2), String)
                rw(5) = DateTime.ParseExact(CType(msg.message4, String), "dd/MM/yyyy", Nothing)
                rw(6) = DateTime.ParseExact(CType(msg.message5, String), "dd/MM/yyyy", Nothing)
                tbtmp.Rows.Add(rw)
                'Add to database
                If mDBManager.AddMainCarparkConfig(tbtmp) <> 1 Then
                    Dim str As String = String.Format("Type: {0}, MainConfig CarparkNO: {1}",
                    msg.TransactionType, rw(0))
                    replyDownloadMsgError(msg.TransactionType, rw(0), str)
                End If
            Case ComCodes.TransactionTypeSelections.DownloadReceiptMsg
                'Dim tbtmp As New DataTable
                'Dim i As Integer
                'For i = 0 To 4
                '    tbtmp.Columns.Add()
                'Next
                'Dim rw As DataRow = tbtmp.NewRow
                ''Dim strr() As String = CType(msg.message1, String).Split(";"c)
                'rw(0) = msg.message1
                'rw(1) = msg.message2
                'rw(2) = msg.message3
                'rw(3) = msg.message4
                'rw(4) = CType(msg.message5, Boolean)
                'tbtmp.Rows.Add(rw)
                ''Add to database
                'If mDBManager.AddReceiptMessage(tbtmp) <> 1 Then
                '    Dim str As String = String.Format("Type: {0}", _
                '    msg.TransactionType)
                '    replyDownloadMsgError(msg.TransactionType, rw(0), str)
                'End If
            Case ComCodes.TransactionTypeSelections.BlockEntryDueToFull
                If CType(msg.message1, Short) = mPGS.InformedBy Then
                    If mTermInformation.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM Then
                        mPGSFullSignOn = CType(msg.message2, Boolean)
                        'mFullSignOn = mPGSFullSignOn Or mManualFullSignOn
                        'mFullSignOn = CType(msg.message2, Boolean)
                        If FullSignOn = True Then
                            mHDwareMgr.TurnOnFullSign()
                        Else
                            mHDwareMgr.TurnOffFullSign()
                        End If

                        mDBManager.UpdateFullStatusRecord(mTermInformation.TerminalNo, FullSignOn)
                        Dim subcbn As Short = IIf(mTermInformation.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM, mTermInformation.DestinationSubCarpark, mTermInformation.SourceSubCarpark)
                        Dim fsign As Object = mDBManager.FindFullSignReserved(subcbn)
                        If fsign Is Nothing Then
                            mFullSignBuffer = 0
                        Else
                            mFullSignBuffer = CType(fsign, Short)
                        End If
                        If mHDwareMgr.VloopStatus = LoopState.LOOPON Then
                            'If Not Eps Is Nothing Then
                            '    If CType(Eps, EpsService).HasCHU = True Then
                            '        'CType(FullEps, EpsService).ResetLastDetected()
                            '    End If
                            'ElseIf Not SemiEps Is Nothing Then
                            '    'CType(SemiEps, SemiEPSService).ResetLastDetected()
                            'End If
                            SyncLock mEntitiesProcessing.SyncRoot
                                If mEntitiesProcessing.Count > 0 Then
                                    mEntitiesProcessing.Clear()
                                End If
                            End SyncLock
                        End If
                    End If
                End If
            Case ComCodes.TransactionTypeSelections.DownloadCyclingCapList
                'Dim tbtmp As New DataTable
                'Dim i As Integer
                'For i = 0 To 4
                '    tbtmp.Columns.Add()
                'Next
                'Dim rw As DataRow = tbtmp.NewRow
                'rw(0) = CType(msg.message1, Integer)
                'rw(1) = msg.message2
                'rw(2) = msg.message3
                'rw(3) = CType(msg.message4, Single)
                'rw(4) = CType(msg.message5, Integer)
                'tbtmp.Rows.Add(rw)
                ''Add to database
                'If mDBManager.AddCyclingCapList(tbtmp) <> 1 Then
                '    Dim str As String = String.Format("Type: {0}, CyclingCap ID: {1}, Rate ID: {2}", _
                '    msg.TransactionType, rw(0), rw(4))
                '    replyDownloadMsgError(msg.TransactionType, rw(0), str)
                'Else
                '    Dim funct1 As New RetrieveFromDB(AddressOf mDBManager.RetrieveAllCyclingCapList)
                '    VerifyIfFinished(ComCodes.TransactionTypeSelections.DownloadCyclingCapList, ComCodes.TransactionTypeSelections.ReplyCyclingCapListCount, funct1)
                'End If
            Case ComCodes.TransactionTypeSelections.DownloadCrossOverList
                'Dim tbtmp As New DataTable
                'Dim i As Integer
                'For i = 0 To 5
                '    tbtmp.Columns.Add()
                'Next
                'Dim rw As DataRow = tbtmp.NewRow
                'Dim strr() As String = CType(msg.message1, String).Split(";"c)
                'rw(0) = CType(strr(0), Integer)
                'rw(1) = CType(strr(1), Integer)
                'rw(2) = strr(2)
                'strr = CType(msg.message2, String).Split(";"c)
                'rw(3) = CType(strr(0), Integer)
                'rw(4) = CType(strr(1), Integer)
                'rw(5) = CType(strr(2), Integer)
                'tbtmp.Rows.Add(rw)
                ''Add to database
                'If mDBManager.AddCrossOverList(tbtmp) <> 1 Then
                '    Dim str As String = String.Format("Type: {0}, CrossOver ID: {1}, Rate ID: {2}", _
                '    msg.TransactionType, rw(0), rw(5))
                '    replyDownloadMsgError(msg.TransactionType, rw(0), str)
                'Else
                '    Dim funct1 As New RetrieveFromDB(AddressOf mDBManager.RetrieveAllCrossOverList)
                '    VerifyIfFinished(ComCodes.TransactionTypeSelections.DownloadCrossOverList, ComCodes.TransactionTypeSelections.ReplyCrossOverListCount, funct1)
                'End If
            Case ComCodes.TransactionTypeSelections.DownloadCyclingRateList
                'Dim tbtmp As New DataTable
                'Dim i As Integer
                'For i = 0 To 9
                '    tbtmp.Columns.Add()
                'Next
                'Dim rw As DataRow = tbtmp.NewRow
                'Dim strr() As String = CType(msg.message1, String).Split(";"c)
                'rw(0) = CType(strr(0), Integer)
                'rw(1) = CType(strr(1), Integer)
                'rw(2) = CType(strr(2), Integer)
                'rw(3) = CType(strr(3), Boolean)
                'rw(4) = CType(strr(4), Integer)
                'strr = CType(msg.message2, String).Split(";"c)
                'rw(5) = CType(strr(0), Integer)
                'rw(6) = CType(strr(1), Integer)
                'rw(7) = CType(strr(2), Integer)
                'rw(8) = CType(strr(3), String)
                'rw(9) = CType(strr(4), Boolean)
                'tbtmp.Rows.Add(rw)
                ''Add to database
                'If mDBManager.AddCyclingRateList(tbtmp) <> 1 Then
                '    Dim str As String = String.Format("Type: {0}, Rate ID: {1}", _
                '                        msg.TransactionType, rw(0))
                '    replyDownloadMsgError(msg.TransactionType, rw(0), str)
                'Else
                '    Dim funct1 As New RetrieveFromDB(AddressOf mDBManager.RetrieveAllCyclingRateList)
                '    VerifyIfFinished(ComCodes.TransactionTypeSelections.DownloadCyclingRateList, ComCodes.TransactionTypeSelections.ReplyCyclingRateListCount, funct1)
                'End If
            Case ComCodes.TransactionTypeSelections.ClearAllCyclingBlockList
                'If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                '    mDBManager.DeleteAllCyclingBlockList()
                'End If
            Case ComCodes.TransactionTypeSelections.ClearAllRateComposite
                'If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                '    mDBManager.DeleteAllRateComposite()
                'End If
            Case ComCodes.TransactionTypeSelections.ClearAllFullStatus
                If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                    mDBManager.DeleteAllFullStatus()
                End If
            Case ComCodes.TransactionTypeSelections.ClearAllAdvancedBlockList
                'If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                '    mDBManager.DeleteAllAdvancedBlockList()
                'End If
            Case ComCodes.TransactionTypeSelections.ClearAllDiscreteBlockList
                'If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                '    mDBManager.DeleteAllDiscreteBlockList()
                'End If
            Case ComCodes.TransactionTypeSelections.ClearAllCyclingCapList
                'If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                '    mDBManager.DeleteAllCyclingCapList()
                'End If
            Case ComCodes.TransactionTypeSelections.ClearAllCrossOverList
                'If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                '    mDBManager.DeleteAllCrossOverList()
                'End If
            Case ComCodes.TransactionTypeSelections.ClearAllCyclingRateList
                'If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                '    mDBManager.DeleteAllCyclingRateList()
                'End If
            Case ComCodes.TransactionTypeSelections.CheckSubCarparkCount
                'mDownloadGeneralInfo(msg.TransactionType) = CType(msg.message1, Integer)
                ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplySubCarparkCount, mDBManager.RetrieveAllSubCarparks.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckWorkingModeCount
                'mDownloadGeneralInfo(msg.TransactionType) = CType(msg.message1, Integer)
                ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyWorkingModeCount, mDBManager.RetrieveAllWorkingModes.Rows.Count, False)
            Case ComCodes.TransactionTypeSelections.CheckTerminalCount
                'mDownloadGeneralInfo(msg.TransactionType) = CType(msg.message1, Integer)
                ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyTerminalCount, mDBManager.RetrieveAllTerminals.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckTerminalRelationCount
                'mDownloadGeneralInfo(msg.TransactionType) = CType(msg.message1, Integer)
                ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyTerminalRelationCount, mDBManager.RetrieveAllTerminalRelations.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckSeasonGroupCount
                'mDownloadGeneralInfo(msg.TransactionType) = CType(msg.message1, Integer)
                ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplySeasonGroupCount, mDBManager.RetrieveAllSeasonGroup.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckComplimentaryCount
                'mDownloadGeneralInfo(msg.TransactionType) = CType(msg.message1, Integer)
                'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyComplimentaryCount, mDBManager.RetrieveAllComplimentary.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckRedemptionCount
                'mDownloadGeneralInfo(msg.TransactionType) = CType(msg.message1, Integer)
                'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyRedemptionCount, mDBManager.RetrieveAllRedemption.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckGroupSeasonPermitsCount
                'mDownloadGeneralInfo(msg.TransactionType) = CType(msg.message1, Integer)
                ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyGroupSeasonPermitsCount, mDBManager.RetrieveAllGroupSeasonPermits.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckCrossOverListCount
                'mDownloadGeneralInfo(msg.TransactionType) = CType(msg.message1, Integer)
                ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyCrossOverListCount, mDBManager.RetrieveAllCrossOverList.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckCyclingBlockListCount
                'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyCyclingBlockListCount, mDBManager.RetrieveAllCyclingBlockList.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckCyclingCapListCount
                'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyCyclingCapListCount, mDBManager.RetrieveAllCyclingCapList.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckCyclingRateListCount
                'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyCyclingRateListCount, mDBManager.RetrieveAllCyclingRateList.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckRateCompositeCount
                'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyRateCompositeCount, mDBManager.RetrieveAllRateComposite.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckAllFullStatusCount
                ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyAllFullStatusCount, mDBManager.RetrieveAllFullStatus.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckDiscreteBlockListCount
                'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyAllDiscreteBlockListCount, mDBManager.RetrieveAllDiscreteBlockList.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckAdvancedBlockListCount
                'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyAdvancedBlockListCount, mDBManager.RetrieveAllAdvancedBlockList.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckRateSetCount
                'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyRateSetCount, mDBManager.RetrieveAllRateSet.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckHolidayCount
                'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyHolidayCount, mDBManager.RetrieveAllHoliday.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckRunningHourlyCount
                'ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyRunningHourlyCount, mDBManager.RetrieveAllRunningHourly.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckRunningSeasonCount
                ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyRunningSeasonCount, mDBManager.RetrieveAllRunningSeason.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckBlacklistedTicketCount
                ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyBlacklistedTicketCount, mDBManager.RetrieveAllBlacklistedTicket.Rows.Count)
            Case ComCodes.TransactionTypeSelections.RequestGenerateService
                Dim workmds() As WorkingMode
                Dim subcbn As Short = IIf(mTermInformation.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM, mTermInformation.DestinationSubCarpark, mTermInformation.SourceSubCarpark)
                If mDBManager.GetWorkingModeForTerminal(mTermInformation.CarparkNo, subcbn, workmds) = True Then
                    GenerateServices(workmds)
                End If
                SendSimpleCmdToServer(ComCodes.TransactionTypeSelections.ReplyGenerateService, DeviceType.CMC_Device, mDeviceBone.SMCIPAddress, False)
            Case ComCodes.TransactionTypeSelections.RequestCheckAllHardwareStatus
                Dim fatalError As Boolean = False
                If mHDwareMgr.SelfTest() = SelfTestResult.FATALREMERROR Then
                    fatalError = True
                End If

                mHDwareMgr.DetectInterval = My.Settings.DetectInterval

                If fatalError = True Then
                    'stop the system 'Implemented later
                Else
                    mAntennaID = mDBManager.FindAntennaID(mTermInformation.TerminalNo)
                    mAntennaNo = mDBManager.FindAntennaNo(mTermInformation.TerminalNo)

                    SendSimpleCmdToServer(ComCodes.TransactionTypeSelections.CheckAllHardwareStatusOK, DeviceType.CMC_Device, mDeviceBone.SMCIPAddress)
                End If

                If fatalError = False Then
                    Dim i As Integer
                    For i = 0 To mServices.Count - 1
                        Select Case CType(mServices(i), ITerminalService).selftest()
                            Case SelfTestResult.FATALCASHCARDERROR, SelfTestResult.FATALEPSERROR,
                                     SelfTestResult.FATALREMERROR
                                fatalError = True
                        End Select
                    Next
                End If

                'mHDwareMgr.TerminalType = mTermInformation.TerminalType
            Case ComCodes.TransactionTypeSelections.DeleteComplimentary
                'If mDBManager.DeleteComplimentary(msg.message1) <> 1 Then
                '    Dim str As String = String.Format("Type: {0}, ComplimentaryNo: {1}", _
                '                        msg.TransactionType, msg.message1)
                '    replyDownloadMsgError(msg.TransactionType, msg.message1, str)
                'End If
            Case ComCodes.TransactionTypeSelections.DeleteHoliday
                'If mDBManager.DeleteHolidayRecord(msg.message1) <> 1 Then
                '    Dim str As String = String.Format("Type: {0}, HolidayID: {1}", _
                '                        msg.TransactionType, msg.message1)
                '    replyDownloadMsgError(msg.TransactionType, msg.message1, str)
                'End If
            Case ComCodes.TransactionTypeSelections.DeleteRedemption
                'RemoveRedemption(msg.message1)
                'DBManager.DoSeasonDBOps(msg.message1, Parkrite.DatabaseManager.DBAction.DELETE, Parkrite.DatabaseManager.DBSeasonDataType.Redemption)

                ''If mDBManager.DeleteRedemption(msg.message1) <> 1 Then
                ''    Dim str As String = String.Format("Type: {0}, ComplimentaryNo: {1}", _
                ''                        msg.TransactionType, msg.message1)
                ''    replyDownloadMsgError(msg.TransactionType, msg.message1, str)
                ''End If
            Case ComCodes.TransactionTypeSelections.DeleteSeasonGroup
                RemoveGroupSeasonInfo(msg.message1)
                DBManager.DoSeasonDBOps(msg.message1, DatabaseManager.DBAction.DELETE, DatabaseManager.DBSeasonDataType.SeasonGroup)
                'If mDBManager.DeleteSeasonGroup(msg.message1) <> 1 Then
                '    Dim str As String = String.Format("Type: {0}, GroupNo: {1}", _
                '                        msg.TransactionType, msg.message1)
                '    replyDownloadMsgError(msg.TransactionType, msg.message1, str)
                'End If
            Case ComCodes.TransactionTypeSelections.DeleteSeasonGroupPermits
                DeleteGroupSeasonPermits(msg.message2, msg.message4)
                DBManager.DoSeasonDBOps(msg.message1, DatabaseManager.DBAction.DELETE, DatabaseManager.DBSeasonDataType.SeasonGroupPermit)
                'If mDBManager.DeleteSeasonGroupPermit(msg.message1) <> 1 Then
                '    Dim str As String = String.Format("Type: {0}, GroupNo: {1}", _
                '                        msg.TransactionType, msg.message1)
                '    replyDownloadMsgError(msg.TransactionType, msg.message1, str)
                'End If
            Case ComCodes.TransactionTypeSelections.DeleteRunningHourly
                'Dim sttts() As String = CType(msg.message1, String).Split(";"c)
                'RemoveRunning(sttts(0), True)
                'DBManager.DoRunningHourRecord(sttts(0), Parkrite.DatabaseManager.DBAction.DELETE)

            Case ComCodes.TransactionTypeSelections.DeleteRunningSeason
                Dim sttts() As String = CType(msg.message1, String).Split(";"c)
                RemoveRunning(sttts(0), False)
                DBManager.DoRunningSeasonRecord(sttts(0), Parkrite.DatabaseManager.DBAction.DELETE)
            Case ComCodes.TransactionTypeSelections.DeleteBlacklistedTicket
                RemoveBlackListed(msg.message1)
                If mDBManager.DeleteBlackListTicket(msg.message1) <> 1 Then
                    Dim str As String = String.Format("Type: {0}, BlacklistNo: {1}",
                                        msg.TransactionType, msg.message1)
                    replyDownloadMsgError(msg.TransactionType, msg.message1, str)
                End If
            Case ComCodes.TransactionTypeSelections.DownloadRunningHourly
                'modifyRunningHourlyDB(msg, ComCodes.TransactionTypeSelections.DownloadRunningHourly)
            Case ComCodes.TransactionTypeSelections.RequestDetectionWhenVloopOn
                If mHDwareMgr.VloopStatus = LoopState.LOOPON Then
                    If Not Eps Is Nothing Then
                        If CType(Eps, EpsService).HasCHU = True Then
                            'CType(FullEps, EpsService).ResetLastDetected()
                        End If
                    End If
                End If
                'If CType(msg.message1, String) = "add" Then
                '    If mHDwareMgr.VloopStatus = LoopState.LOOPON Then
                '        If Not FullEps Is Nothing Then
                '            If CType(FullEps, FullEPSService).HasCHU = True Then
                '                CType(FullEps, FullEPSService).ResetLastDetected()
                '            End If
                '        ElseIf Not SemiEps Is Nothing Then
                '            CType(SemiEps, SemiEPSService).ResetLastDetected()
                '        End If
                '    End If
                'Else
                '    If mFullSignOn = True Then
                '        mFullSignBuffer += 1
                '        If mHDwareMgr.VloopStatus = LoopState.LOOPON Then
                '            If Not FullEps Is Nothing Then
                '                If CType(FullEps, FullEPSService).HasCHU = True Then
                '                    CType(FullEps, FullEPSService).ResetLastDetected()
                '                End If
                '            ElseIf Not SemiEps Is Nothing Then
                '                CType(SemiEps, SemiEPSService).ResetLastDetected()
                '            End If
                '        End If
                '    End If
                'End If
            Case ComCodes.TransactionTypeSelections.DownloadRunningSeason
                modifyRunningSeasonDB(msg, ComCodes.TransactionTypeSelections.DownloadRunningSeason)
            Case ComCodes.TransactionTypeSelections.UpdateRunningHourly
                'modifyRunningHourlyDB(msg, ComCodes.TransactionTypeSelections.UpdateRunningHourly)
            Case ComCodes.TransactionTypeSelections.UpdateRunningSeason
                modifyRunningSeasonDB(msg, ComCodes.TransactionTypeSelections.UpdateRunningSeason)
            Case ComCodes.TransactionTypeSelections.DownloadAllBlacklistedTicket
                modifyBlacklistedTicketDB(msg, ComCodes.TransactionTypeSelections.DownloadAllBlacklistedTicket)
            Case ComCodes.TransactionTypeSelections.UpdateAllBlacklistedTicket
                modifyBlacklistedTicketDB(msg, ComCodes.TransactionTypeSelections.UpdateAllBlacklistedTicket)
            Case ComCodes.TransactionTypeSelections.RequestCloseBarrier
                mHDwareMgr.CloseBarrier()
                SendSimpleCmdToServer(ComCodes.TransactionTypeSelections.ReplyCloseBarrierOK, DeviceType.CMC_Device, mDeviceBone.SMCIPAddress)
            Case ComCodes.TransactionTypeSelections.RequestOpenBarrier
                SendSimpleCmdToServer(ComCodes.TransactionTypeSelections.CommandBarrierOpen, DeviceType.CMC_Device, mDeviceBone.SMCIPAddress)
                mHDwareMgr.OpenBarrier()
                SendSimpleCmdToServer(ComCodes.TransactionTypeSelections.ReplyOpenBarrierOK, DeviceType.CMC_Device, mDeviceBone.SMCIPAddress)
            Case ComCodes.TransactionTypeSelections.ClearAllRateList
                'If Not mRateList Is Nothing Then
                '    mRateList.Clear()
                'End If
            Case ComCodes.TransactionTypeSelections.ClearAllRateSetList
                'If mTermType <> NetworkReferences.DeviceServiceDefinition.DeviceType.UNCONFIGURED Then
                '    mDBManager.DeleteAllRateSets()
                'End If
            Case ComCodes.TransactionTypeSelections.RequestSetFullSignOn
                If mTermInformation.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM Then
                    mManualFullSignOn = True
                    If FullSignOn = True Then
                        mHDwareMgr.TurnOnFullSign()
                    End If
                    'mDBManager.UpdateFullStatusRecord(mTermInformation.TerminalNo, True)
                    Dim subcbn As Short = IIf(mTermInformation.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM, mTermInformation.DestinationSubCarpark, mTermInformation.SourceSubCarpark)
                    Dim fsign As Object = mDBManager.FindFullSignReserved(subcbn)
                    If fsign Is Nothing Then
                        mFullSignBuffer = 0
                    Else
                        mFullSignBuffer = CType(fsign, Short)
                    End If
                    If mHDwareMgr.VloopStatus = LoopState.LOOPON Then
                        'If Not Eps Is Nothing Then
                        '    If CType(Eps, EpsService).HasCHU = True Then
                        '        'CType(FullEps, FullEPSService).ResetLastDetected()
                        '    End If
                        'ElseIf Not SemiEps Is Nothing Then
                        '    'CType(SemiEps, EpsService).ResetLastDetected()
                        'End If
                    End If
                End If
            Case ComCodes.TransactionTypeSelections.RequestSetFullSignOff
                If mTermInformation.TerminalType = Enumeration.TERMINALOPTION.ENTRYTERM Then
                    mManualFullSignOn = False
                    'mFullSignOn = mPGSFullSignOn Or mManualFullSignOn
                    If FullSignOn = False Then
                        mHDwareMgr.TurnOffFullSign()
                        mDBManager.UpdateFullStatusRecord(mTermInformation.TerminalNo, False)
                        If mHDwareMgr.VloopStatus = LoopState.LOOPON Then
                            SyncLock mEntitiesProcessing.SyncRoot
                                If mEntitiesProcessing.Count > 0 Then
                                    mEntitiesProcessing.Clear()
                                End If
                            End SyncLock
                        End If
                    End If
                End If
                '**************added by saw****************************
            Case ComCodes.TransactionTypeSelections.RequestProgramLEDMsg
                'ProgLED(msg, ComCodes.TransactionTypeSelections.RequestProgramLEDMsg)
                'SendSimpleCmdToServer(ComCodes.TransactionTypeSelections.ReplyProgramLEDMsg, DeviceType.CMC_Device, mDeviceBone.SMCIPAddress)
                '******************************************************

            Case ComCodes.TransactionTypeSelections.RequestCheckLink
                Dim themsg As New TransactionMsg
                themsg.TransactionType = ComCodes.TransactionTypeSelections.ReplyCheckLinkOk
                'themsg.message1 = mTermInformation.TerminalName
                themsg.message2 = mTermInformation.TerminalNo
                'themsg.message3 = mDBManager.RetrieveAllRunningHourly.Rows.Count
                'themsg.message4 = mDBManager.RetrieveAllIUSeasonInfo.Rows.Count
                'themsg.message5 = mDBManager.RetrieveAllCashCardSeasonInfo.Rows.Count    'added by JL
                themsg.message3 = RunningHourlyCount()
                If HasEps() = True Then
                    themsg.message4 = Eps.SeasonCount()
                Else
                    themsg.message4 = 0
                End If
                If HasCashCardSys() = True Then
                    'themsg.message5 = CashCardSys.SeasonCount()
                Else
                    themsg.message5 = 0
                End If
                themsg.mLog = False
                Me.SendMsgToSMC(themsg)
            Case ComCodes.TransactionTypeSelections.DownloadPGSInfo
                modifyPGSDB(msg, ComCodes.TransactionTypeSelections.DownloadPGSInfo)
            Case ComCodes.TransactionTypeSelections.DownloadAddonOption
                mDBManager.DeleteAllAddonOption()
                Dim addon As AddonOption
                Dim strs() As String = CType(msg.message1, String).Split(";"c)
                addon.LEDEnabled = strs(0)
                addon.ShutterEnabled = strs(1)
                addon.CashcardConfirm = strs(2)
                addon.PowerFailAlarm = strs(3)
                addon.SeasonAllowedWhenFull = strs(4)
                addon.TailGateSensor = strs(5)
                addon.SubCPWithinSubCP = strs(6)
                If mDBManager.AddAddonOptionRecords(addon) <> 1 Then
                    Dim str As String = String.Format("Type: {0}",
                                                    msg.TransactionType)
                    replyDownloadMsgError(msg.TransactionType, strs(0), str)
                End If
            Case ComCodes.TransactionTypeSelections.CheckAddonCount
                ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyAddonOptionCount, mDBManager.RetrieveAllAddonOption.Rows.Count)
            Case ComCodes.TransactionTypeSelections.CheckPGSInfoCount
                ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyPGSInfoCount, mDBManager.RetrieveAllPGSInfo.Rows.Count)
            Case ComCodes.TransactionTypeSelections.RequestPGSEnabled
                mDBManager.DeleteAllPGSEnabled()
                mDBManager.AddPGSEnabled(CType(msg.message1, Boolean))
                Dim themsg As New TransactionMsg
                themsg.TransactionType = ComCodes.TransactionTypeSelections.ReplyPGSEnabled
                themsg.message1 = mDBManager.PGSIsEnabled()
                themsg.mLog = False
                SendMsgToSMC(themsg)
            Case ComCodes.TransactionTypeSelections.DownloadAllFullStatus
                Dim tbtmp As New DataTable
                Dim i As Integer
                For i = 0 To 1
                    tbtmp.Columns.Add()
                Next
                Dim rw As DataRow = tbtmp.NewRow
                rw(0) = CType(msg.message1, Short)
                rw(1) = CType(msg.message2, Boolean)
                tbtmp.Rows.Add(rw)
                'Add to database
                If mDBManager.AddFullStatusRecords(tbtmp) <> 1 Then
                    Dim str As String = String.Format("Type: {0}, Terminal ID: {1}",
                    msg.TransactionType, rw(0))
                    replyDownloadMsgError(msg.TransactionType, rw(0), str)
                End If
            Case ComCodes.TransactionTypeSelections.NotifySeasonEntryNonAccessedTerminal
                Dim accc As AccessViolation
                accc.TicketNo = msg.message1
                accc.EntryTime = DateTime.ParseExact(CType(msg.message2, String), "dd/MM/yyyy HH:mm:ss", Nothing)
                accc.TerminalNo = msg.message3
                accc.RelationID = -1
                If IsToTerminal = True Then
                    If IsRelatedFromTerminal(accc.TerminalNo) <> -1 Then
                        If mDBManager.AddAccessViolationRecords(New AccessViolation() {accc}) <> 1 Then
                            Dim str As String = String.Format("Type: {0}, Ticket No: {1}, EntryTime: {2}, TerminalNo: {3}",
                                                msg.TransactionType, accc.TicketNo, msg.message2, accc.TerminalNo)
                            replyDownloadMsgError(msg.TransactionType, accc.TicketNo, str)
                        End If
                    End If
                End If
            Case ComCodes.TransactionTypeSelections.NotifySeasonEntryAccessedTerminal
                Dim accc As AccessViolation
                accc.TicketNo = msg.message1
                accc.EntryTime = DateTime.ParseExact(CType(msg.message5, String), "dd/MM/yyyy HH:mm:ss", Nothing)
                accc.TerminalNo = msg.message4
                If IsToTerminal = True Then
                    If IsRelatedFromTerminal(accc.TerminalNo) <> -1 Then
                        Dim avrd As AccessViolation
                        If DBManager.RetrieveAccessViolationRecord(accc.TicketNo, avrd) = True Then
                            DBManager.DeleteAccessViolationRecord(accc.TicketNo)
                        End If
                    End If
                End If
            Case ComCodes.TransactionTypeSelections.NotifySeasonAccessViolationDebit
                If CType(msg.message6, Short) = mTermInformation.TerminalNo Then
                    Dim acdb As AccessViolationDebit
                    acdb.TicketNo = msg.message1
                    acdb.EntryTime = DateTime.ParseExact(CType(msg.message2, String), "dd/MM/yyyy HH:mm:ss", Nothing)
                    acdb.ExitTime = DateTime.ParseExact(CType(msg.message3, String), "dd/MM/yyyy HH:mm:ss", Nothing)
                    acdb.EntryTerminalNo = msg.message4
                    acdb.ExitTerminalNo = msg.message5
                    'calculate fee
                    If mDBManager.AddAccessViolationDebitRecords(New AccessViolationDebit() {acdb}) <> 1 Then
                        Dim str As String = String.Format("Type: {0}, Ticket No: {1}, EntryTime: {2}, Entry TerminalNo: {3}",
                                                                        msg.TransactionType, acdb.TicketNo, msg.message2, acdb.EntryTerminalNo)
                        replyDownloadMsgError(msg.TransactionType, acdb.TicketNo, str)
                    End If
                End If
            Case ComCodes.TransactionTypeSelections.RequestCheckSeasonCount
                Dim themsg As New TransactionMsg
                themsg.TransactionType = ComCodes.TransactionTypeSelections.ReplySeasonCount
                themsg.message1 = mDBManager.RetrieveAllIUSeasonInfo.Rows.Count
                themsg.message2 = mDBManager.RetrieveAllIUSeasonPermits.Rows.Count
                themsg.mLog = False
                Me.SendMsgToSMC(themsg)
            Case ComCodes.TransactionTypeSelections.RequestDeleteExpRunningRecord
                Dim dtNow As Date = Now
                Dim recdcnt As Integer
                dtNow = DateAdd(DateInterval.Hour, -Integer.Parse(msg.message1), dtNow)
                'recdcnt = mDBManager.DeleteExpRunningHourlyRecord(dtNow)
                mDBManager.DoRunningHourRecord(dtNow, DatabaseManager.DBAction.DELETEBYTIME)
                Dim keystodelete As New ArrayList
                Dim delbf As Integer = RunningHourlyCount()
                SyncLock mRunningHourlyRecords.SyncRoot
                    For Each key As String In mRunningHourlyRecords.Keys
                        Dim info As RunningHourly = mRunningHourlyRecords(key)
                        If info.EntryTime <= dtNow Then
                            keystodelete.Add(key)
                        End If
                    Next
                    For Each key As String In keystodelete
                        mRunningHourlyRecords.Remove(key)
                    Next
                End SyncLock

                Dim delaf As Integer = RunningHourlyCount()
                ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyAllExpRunningHrCount, delbf - delaf)
                'recdcnt = mDBManager.DeleteExpRunningSeasonRecord(dtNow)
                mDBManager.DoRunningSeasonRecord(dtNow, DatabaseManager.DBAction.DELETEBYTIME)
                keystodelete.Clear()
                delbf = RunningSeasonCount()
                SyncLock mRunningSeasonRecords.SyncRoot
                    For Each key As String In mRunningSeasonRecords.Keys
                        Dim info As RunningSeason = mRunningSeasonRecords(key)
                        If info.EntryTime <= dtNow Then
                            keystodelete.Add(key)
                        End If
                    Next
                    For Each key As String In keystodelete
                        mRunningSeasonRecords.Remove(key)
                    Next
                End SyncLock
                delaf = RunningSeasonCount()
                ReplyAllRecordsCount(ComCodes.TransactionTypeSelections.ReplyAllExpRunningSeasCount, delbf - delaf)
            Case 516 To 540
                'Dim tmpcntx As New DownloadGeneralinfo
                'tmpcntx.currentcnt = 0
                'tmpcntx.maxcnt = CType(msg.message1, Integer)
                ''SyncLock mDownloadGeneralInfo.SyncRoot
                'mDownloadGeneralInfo(CType(msg.message2, ComCodes.TransactionTypeSelections)) = tmpcntx
                ''End SyncLock
            Case ComCodes.TransactionTypeSelections.RequestDisablePGS
                mPGSEnabled = False
                If FullSignOn = True Then
                    mHDwareMgr.TurnOnFullSign()
                Else
                    If mHDwareMgr.VloopStatus = LoopState.LOOPON Then
                        SyncLock mEntitiesProcessing.SyncRoot
                            If mEntitiesProcessing.Count > 0 Then
                                mEntitiesProcessing.Clear()
                            End If
                        End SyncLock
                    End If
                    mHDwareMgr.TurnOffFullSign()
                End If
                Console.WriteLine("RequestDisablePGS received")
            Case ComCodes.TransactionTypeSelections.RequestEnablePGS
                mPGSEnabled = True
                If FullSignOn = True Then
                    mHDwareMgr.TurnOnFullSign()
                Else
                    If mHDwareMgr.VloopStatus = LoopState.LOOPON Then
                        SyncLock mEntitiesProcessing.SyncRoot
                            If mEntitiesProcessing.Count > 0 Then
                                mEntitiesProcessing.Clear()
                            End If
                        End SyncLock
                    End If
                    mHDwareMgr.TurnOffFullSign()
                End If
                Console.WriteLine("RequestEnablePGS received")
            Case ComCodes.TransactionTypeSelections.RequestBypassFullSign
                mBypassFullSign = True
                mBypassFullSignTimer = New Threading.Timer(AddressOf bypassfullsignproc, Nothing, CType(msg.message1, Integer) * 1000, Threading.Timeout.Infinite)
                Dim themsg As New TransactionMsg
                themsg.TransactionType = ComCodes.TransactionTypeSelections.ReplyBypassFullSign
                themsg.mLog = False
                Me.SendMsgToSMC(themsg, True)
                If FullSignOn = True Then
                    mHDwareMgr.TurnOnFullSign()
                Else
                    If mHDwareMgr.VloopStatus = LoopState.LOOPON Then
                        SyncLock mEntitiesProcessing.SyncRoot
                            If mEntitiesProcessing.Count > 0 Then
                                mEntitiesProcessing.Clear()
                            End If
                        End SyncLock
                    End If
                    mHDwareMgr.TurnOffFullSign()
                End If
                If mTermType = DeviceType.Entry_Device And mHDwareMgr.VloopStatus = Enumeration.LoopState.LOOPOFF Then
                    Dim curiu As String = Nothing
                    Try
                        'curiu = mHDwareMgr.ReadIUFromSerialPort()
                    Catch ex As Exception

                    End Try

                    If curiu = "OFFLINE" Then
                        curiu = Nothing
                    ElseIf curiu = "1096000001" Then
                        curiu = Nothing
                    End If

                    If HasEps() = True Then
                        CType(Eps, EPSEntryService).IUDetectedwhenBypassFull(curiu)
                    End If
                    Console.WriteLine("RequestBypassFullSign: Realtime detected iu: " & curiu)
                End If
            Case ComCodes.TransactionTypeSelections.RequestReStartAntenna
                mHDwareMgr.RestartAntenna()
                Dim themsg As New TransactionMsg
                themsg.TransactionType = ComCodes.TransactionTypeSelections.ReplyReStartAntenna
                themsg.mLog = False
                Me.SendMsgToSMC(themsg)
            Case ComCodes.TransactionTypeSelections.RequestTerminalFullStatus
                Dim themsg As New TransactionMsg
                themsg.TransactionType = ComCodes.TransactionTypeSelections.ReplyTerminalFullStatus
                themsg.message1 = mManualFullSignOn
                themsg.message2 = mFullSignOn
                themsg.message3 = mPGSFullSignOn & ";" & False
                themsg.message4 = mBypassFullSign
                themsg.message5 = mIgnoreFullSign
                themsg.message6 = mPGSEnabled
                themsg.mLog = False
                Me.SendMsgToSMC(themsg)
        End Select
    End Sub

    Private Sub modifyRunningSeasonDB(ByVal msg As TransactionMsg, ByVal transtype As ComCodes.TransactionTypeSelections)
        Dim tmprunning As New RunningSeason
        tmprunning.SeasonNo = msg.message1
        tmprunning.EntryTime = DateTime.ParseExact(msg.message2, "dd/MM/yyyy HH:mm:ss", Nothing)
        tmprunning.TerminalNo = msg.message3
        Dim strr() As String = CType(msg.message4, String).Split(";"c)
        tmprunning.SeasonOption = strr(0)
        tmprunning.SeasonType = strr(1)
        tmprunning.Status = msg.message5
        If IsTerminalInsideThisCarpark(tmprunning.TerminalNo) Then
            UpdateRunning(tmprunning)
            mDBManager.DoRunningSeasonRecord(tmprunning, DatabaseManager.DBAction.UPDATE)
        End If
    End Sub

    Private Sub modifyseasonGroupPermitsDB(ByVal msg As TransactionMsg, ByVal transtype As ComCodes.TransactionTypeSelections)
        Dim theinfor As New GroupSeasonPermits
        theinfor.Id = CType(msg.message1, Integer)
        theinfor.GroupNo = CType(msg.message2, Integer)
        theinfor.CarparkNo = CType(msg.message3, Short)
        theinfor.SubCarparkNo = CType(msg.message4, Short)
        UpdateGroupSeasonPermits(theinfor)
        DBManager.DoSeasonDBOps(theinfor, DatabaseManager.DBAction.UPDATE, DatabaseManager.DBSeasonDataType.SeasonGroupPermit)
    End Sub

    Private Sub modifyseasonGroupDB(ByVal msg As TransactionMsg, ByVal transtype As ComCodes.TransactionTypeSelections)
        Dim theinfor As New ClsSeasonGroup
        theinfor.GroupNo = msg.message1
        'theinfor.GroupName = msg.message2
        Dim strr() As String = CType(msg.message3, String).Split(";"c)
        theinfor.Maximum = CType(strr(0), Integer)
        theinfor.Threshold = CType(strr(1), Integer)
        theinfor.CurrentCount = CType(strr(2), Integer)
        'strr = CType(msg.message4, String).Split(";"c)
        'theinfor.Phone = strr(0)
        'theinfor.Fax = strr(1)
        'theinfor.Address = msg.message5
        'theinfor.Remark = msg.message6

        UpdateGroupSeasonInfo(theinfor)
        DBManager.DoSeasonDBOps(theinfor, DatabaseManager.DBAction.UPDATE, DatabaseManager.DBSeasonDataType.SeasonGroup)
    End Sub

    Private Sub ReadExpDay()
        If System.IO.File.Exists(daytoexpir) Then
            If New IO.FileInfo(daytoexpir).Length <> 0 Then
                Dim textstream As New IO.FileStream(daytoexpir, IO.FileMode.Open)
                Dim textReader As New IO.StreamReader(textstream)
                Try
                    expday = Val(textReader.ReadLine)
                Catch ex As Exception
                    expday = 7
                End Try
            Else
                expday = 7
            End If
        Else
            expday = 7
        End If
    End Sub

    Private Sub IdentityAuthenticateSucceedHandler(ByVal service As ITerminalService, ByVal ticket As String, ByVal runstat As RunningStatus)
        If mFullSignBuffer > 0 Then
            mFullSignBuffer -= 1
        End If

        mHDwareMgr.OpenBarrier()
    End Sub

    Private Sub HandleVlpOn(ByVal iu As String)
        Console.WriteLine("Vloop on")

        ''zaw 16/08/2018 disabled it below,solved the issue of 78 shelton way
        ''no need to send iu No while vloop starts time, 
        ''to avoid the last vehicle record sending to SMC while vloop start time and prevent to wrongly open the barrier for other non season vehicle.
        'If Not iu Is Nothing Then
        '    Console.WriteLine("Vloop on IU =" & iu)
        '    RaiseEvent IUIsDetected(iu)
        'End If
        '''''''''''''''''''''''16/08/2018''''''''''''''''''''''''''''''''''''''''''

        'mTimeLastVloopOn = Now
        'mTailGateSensorTurnToOffNum = 0
        'Dim vloopOnThread As Threading.Thread = New Threading.Thread(AddressOf VloopOnProc)
        'vloopOnThread.Start()
    End Sub

    Private Sub VloopOnProc()
        'If mSysMode = Enumeration.SystemMode.ACTIVE Then
        '    Dim pair As New LoopPairStatus
        '    pair.vloop = LoopState.LOOPON
        '    pair.nloop = mHDwareMgr.NloopStatus
        '    pair.timeLabel = DateTime.Now
        '    mLoopPairs.Enqueue(pair)
        '    If mLoopPairs.Count = 5 Then
        '        mLoopPairs.Dequeue()
        '    End If
        '    'Send VloopOn message to HPC
        '    'Dim trans As TransactionMsg
        '    'trans.TransactionType = ComCodes.TransactionTypeSelections.HPCInformVloopOn
        '    'SendMsgToHPC(trans)
        '    If blnRecPrinted = True Then
        '        blnRecPrinted = False
        '    End If
        '    mHDwareMgr.LCDBackLightOn()
        '    RaiseEvent VloopIsOn()
        'End If
        'Dim ah As Integer = System.Threading.Thread.CurrentThread.GetHashCode()
        'Console.WriteLine("Vloop On - {0}", ah)
    End Sub

    Private Sub HandleVlpOff()
        Console.WriteLine("Vloop off")
        Dim vloopOffThread As Threading.Thread = New Threading.Thread(AddressOf VloopOffProc)
        vloopOffThread.Start()
    End Sub

    Private Sub VloopOffProc()
        'If mSysMode = Enumeration.SystemMode.ACTIVE Then
        '    Dim pair As New LoopPairStatus
        '    pair.vloop = LoopState.LOOPOFF
        '    pair.nloop = mHDwareMgr.NloopStatus
        '    pair.timeLabel = DateTime.Now
        '    mLoopPairs.Enqueue(pair)
        '    If mLoopPairs.Count = 5 Then
        '        mLoopPairs.Dequeue()
        '    End If


        '    RaiseEvent VloopIsOff()

        'End If
    End Sub

    Private Sub HandleNlpOn()
        Console.WriteLine("Nloop on")
        'mNloopWasEverOn = True
        'Dim nloopOnThread As Threading.Thread = New Threading.Thread(AddressOf NloopOnProc)
        'nloopOnThread.Start()
    End Sub

    Private Sub NloopOnProc()
        'If mSysMode = Enumeration.SystemMode.ACTIVE Then
        '    Dim pair As New LoopPairStatus
        '    pair.vloop = mHDwareMgr.VloopStatus
        '    pair.nloop = LoopState.LOOPON
        '    pair.timeLabel = DateTime.Now
        '    mLoopPairs.Enqueue(pair)
        '    If mLoopPairs.Count = 5 Then
        '        mLoopPairs.Dequeue()
        '    End If
        'End If
        'Console.WriteLine("Nloop On")
    End Sub

    Private Sub HandleNlpOff()
        Console.WriteLine("Nloop off")
        'mNloopWasEverOn = False
        Dim nloopOffThread As Threading.Thread = New Threading.Thread(AddressOf NloopOffProc)
        'If mSysMode = Enumeration.SystemMode.ACTIVE Then
        '    'If mNumOfSuspendOpenBarrier <> 0 Then
        '    '    mNumOfSuspendOpenBarrier -= 1
        '    '    mHDwareMgr.OpenBarrier()
        '    '    mLastOpenbarrierIsSuspending = True
        '    'End If
        'End If
        nloopOffThread.Start()
    End Sub

    Private Sub NloopOffProc()
        If mSysMode = Enumeration.SystemMode.ACTIVE Then
            'Dim pair As New LoopPairStatus
            'pair.vloop = mHDwareMgr.VloopStatus
            'pair.nloop = LoopState.LOOPOFF
            'pair.timeLabel = DateTime.Now
            'mLoopPairs.Enqueue(pair)
            'If mLoopPairs.Count = 5 Then
            '    mLoopPairs.Dequeue()
            'End If


            'Confirm it is a record and send it to SMC
            SendAppendedTrans()
            'If mNumOfSuspendOpenBarrier <> 0 Then
            '    mNumOfSuspendOpenBarrier -= 1
            '    mHDwareMgr.OpenBarrier()
            '    SendSimpleCmdToServer(ComCodes.TransactionTypeSelections.CommandBarrierOpen, DeviceType.CMC_Device, mDeviceBone.SMCIPAddress)
            '    mLastOpenbarrierIsSuspending = True
            '    Dim debugtrans As New TransactionMsg
            '    debugtrans.TransactionType = ComCodes.TransactionTypeSelections.DebugError
            '    debugtrans.mLog = False
            '    debugtrans.message1 = "Barrier is open due to suspending operation when Vloop off"
            '    debugtrans.message2 = mNumOfSuspendOpenBarrier
            '    If mDetectedIUs.Count > 0 Then
            '        debugtrans.message3 = CType(mDetectedIUs(mDetectedIUs.Count - 1), detectedIU).iu
            '        debugtrans.message4 = CType(mDetectedIUs(mDetectedIUs.Count - 1), detectedIU).timeLabel.ToString("dd/MM/yyyy HH:mm:ss")
            '        debugtrans.message5 = CType(mDetectedIUs(mDetectedIUs.Count - 1), detectedIU).sentUp
            '    End If
            '    debugtrans.message6 = mLastOpenbarrierIsSuspending
            '    SendMsgToSMC(debugtrans)
            'Else
            '    mLastOpenbarrierIsSuspending = False
            'End If
            'mNloopTurnToOffNum = 0
            'mTailGateSensorTurnToOffNum = 0
            'If pair.vloop = LoopState.LOOPOFF Then
            '    mDetectedIUs.Clear()
            'End If
            ''mHDwareMgr.CloseBarrier()
            ''Threading.Thread.Sleep(1000)
            ''Console.WriteLine("+++++++++++++++++++++++++++++++++++++++++++++++++++ barrierisopen status = {0}", mHDwareMgr.BarrierIsOpen)
            'mHDwareMgr.CloseShutter()  'Added by Tan wei
            'mShutterIsOpen = False
            'mHDwareMgr.DisplayLCD("WELCOME", 1)
            'mHDwareMgr.LCDBackLightOff()
            ' mHDwareMgr.ClearLED()
            'End If     'comment for send msg

            'RaiseEvent NloopIsOff()     'add Nloop off event
            Console.WriteLine("Nloop Off")


        End If
    End Sub

    Private Sub handleBarrierIsOpen()
        'Removed by tan wei to reduce network traffic
        If mSysMode = Enumeration.SystemMode.ACTIVE Then
            SendSimpleCmdToServer(ComCodes.TransactionTypeSelections.BarrierIsOpen, DeviceType.CMC_Device, mDeviceBone.SMCIPAddress)
        End If
    End Sub

    Private Sub modifyBlacklistedTicketDB(ByVal msg As TransactionMsg, ByVal transtype As ComCodes.TransactionTypeSelections)
        Dim tbtmp As New DataTable
        Dim i As Integer
        For i = 0 To 5
            tbtmp.Columns.Add()
        Next
        Dim rw As DataRow = tbtmp.NewRow
        'For i = 0 To 5
        '    rw(i) = msg.message(i)
        'Next
        rw(0) = CType(msg.message1, String).Trim()
        rw(1) = CType(msg.message2, String).Trim()
        rw(2) = DateTime.ParseExact(msg.message3, "dd/MM/yyyy", Nothing)
        rw(3) = DateTime.ParseExact(msg.message4, "dd/MM/yyyy", Nothing)
        rw(4) = msg.message5
        rw(5) = msg.message6
        tbtmp.Rows.Add(rw)
        Dim blk As BlacklistedTicket
        blk.TicketNo = rw(0)
        blk.TicketType = rw(1)
        blk.ListedDate = rw(2)
        blk.EffectiveDate = rw(3)
        UpdateBlackListed(blk)
        If transtype = ComCodes.TransactionTypeSelections.DownloadAllBlacklistedTicket Then
            If mDBManager.AddBlacklistedTicketRecord(tbtmp) <> 1 Then
                Dim str As String = String.Format("Type: {0}, Ticket No: {1}", _
                msg.TransactionType, rw(0))
                replyDownloadMsgError(msg.TransactionType, rw(0), str)
            Else
                'Dim funct1 As New RetrieveFromDB(AddressOf mDBManager.RetrieveAllBlacklistedTicket)
                'VerifyIfFinished(ComCodes.TransactionTypeSelections.DownloadAllBlacklistedTicket, ComCodes.TransactionTypeSelections.ReplyBlacklistedTicketCount, funct1)
            End If
        Else
            If mDBManager.UpdateBlacklistedTicketRecords(tbtmp) <> 1 Then
                Dim str As String = String.Format("Type: {0}, Ticket No: {1}", _
                msg.TransactionType, rw(0))
                replyDownloadMsgError(msg.TransactionType, rw(0), str)
            End If
        End If
    End Sub

    Private Sub modifyPGSDB(ByVal msg As TransactionMsg, ByVal transtype As ComCodes.TransactionTypeSelections)
        Dim tbtmp As New DataTable
        Dim i As Integer
        For i = 0 To 2
            tbtmp.Columns.Add()
        Next
        Dim rw As DataRow = tbtmp.NewRow
        rw(0) = msg.message1
        rw(1) = msg.message2
        rw(2) = msg.message3
        tbtmp.Rows.Add(rw)
        If transtype = ComCodes.TransactionTypeSelections.DownloadPGSInfo Then
            If mDBManager.AddPGSRecords(tbtmp) <> 1 Then
                Dim str As String = String.Format("Type: {0}, Terminal No: {1}", _
                msg.TransactionType, rw(0))
                replyDownloadMsgError(msg.TransactionType, rw(0), str)
            Else
                'Dim funct1 As New RetrieveFromDB(AddressOf mDBManager.RetrieveAllPGSInfo)
                'VerifyIfFinished(ComCodes.TransactionTypeSelections.DownloadPGSInfo, ComCodes.TransactionTypeSelections.ReplyPGSInfoCount, funct1)
            End If
        Else
            If mDBManager.UpdatePGSRecords(tbtmp) <> 1 Then
                Dim str As String = String.Format("Type: {0}, Terminal No: {1}", _
                msg.TransactionType, rw(0))
                replyDownloadMsgError(msg.TransactionType, rw(0), str)
            End If
        End If
    End Sub

    Private Sub bypassfullsignproc(ByVal obj As Object)
        mBypassFullSign = False
        If FullSignOn = True Then
            mHDwareMgr.TurnOnFullSign()
        Else
            mHDwareMgr.TurnOffFullSign()
        End If
    End Sub

    Private Sub handleDetectedIU(ByVal iu As String)
        RaiseEvent IUIsDetected(iu)
    End Sub

    Private Sub initHardwareManager()
        If IsNothing(mHDwareMgr) = True Then
            mHDwareMgr = New TermHardwareManager(mTermInformation.TerminalNo, mTermInformation.TerminalName, mAntennaPort, mAntennaID, mAntennaType, mRemotePort)
        End If
        AddHandler mDeviceBone.ReceivedEvent, AddressOf ReceiveMessageEvtProcess
        AddHandler mHDwareMgr.VloopIsOn, AddressOf HandleVlpOn
        AddHandler mHDwareMgr.VloopIsOff, AddressOf HandleVlpOff
        AddHandler mHDwareMgr.NloopIsOn, AddressOf HandleNlpOn
        AddHandler mHDwareMgr.NloopIsOff, AddressOf HandleNlpOff
        AddHandler mHDwareMgr.IUDetected, AddressOf handleDetectedIU
        'AddHandler mHDwareMgr.HardwareError, AddressOf HandleHardWareError
        'AddHandler mHDwareMgr.VerifyThreadComplete, AddressOf HandleVerifyThreadComplete
        'AddHandler mHDwareMgr.EvtReceptRequest, AddressOf HandleReceiptRequest
        'AddHandler mHDwareMgr.TailgatingIsOff, AddressOf HandleTailGatingOff
        'AddHandler mHDwareMgr.TailgatingIsOn, AddressOf HandleTailGatingOn
        'AddHandler mHDwareMgr.EvtTransitToBatteryBackup, AddressOf HandleBatteryTransit
        'AddHandler mHDwareMgr.EvtFullSignal, AddressOf HandleEvtFullSignal
        'AddHandler mHDwareMgr.EvtBarrierIsBroken, AddressOf handleBarrierIsBroken
        'AddHandler mHDwareMgr.EvtPrinterError, AddressOf handlePrinterError 'added by saw
        AddHandler mHDwareMgr.EvtBarrierIsOpen, AddressOf handleBarrierIsOpen
        'AddHandler mHDwareMgr.EvtBarrierFromOpenToClose, AddressOf handleBarrierFromOpenToClose
        'AddHandler mHDwareMgr.EvtBarrierOK, AddressOf handleBarrierOK
        'AddHandler mHDwareMgr.EvtPowerSupplyOK, AddressOf handlePowerSupplyOK
        'AddHandler mHDwareMgr.EvtManuallyOpenBarrier, AddressOf handleManuallyOpenBarrier
        'AddHandler mHDwareMgr.EvtIsMotorcycle, AddressOf HandleEvtIsMotorcycle
        'AddHandler mHDwareMgr.EvtIsMotorcycleOff, AddressOf HandleEvtIsMotorcycleOff
    End Sub


    Public Sub StartListeningToServer()
        Console.WriteLine("Establish connection using LPR") 'Start listening for server
        Dim stream As NetworkStream = TCPClient.GetStream()
        Dim data(999) As Byte
        Dim bytes As Integer

        While True
            bytes = stream.Read(data, 0, data.Length)
            If bytes = 0 Then Exit While

            Dim response As String = Encoding.UTF8.GetString(data, 0, bytes)
            Try
                Dim lprData As LprData = Newtonsoft.Json.JsonConvert.DeserializeObject(Of LprData)(response)
                Console.WriteLine($"Deserialized: Plate={lprData?.PlateNumber}, Time={lprData?.Time}, GeneralPath={lprData?.GeneralPath}, SpecificPath={lprData?.SpecificPath}, SequenceId={lprData?.SequenceId}")

                'If lprData IsNot Nothing Then
                '    ' Trigger barrier open only when PlateNumber is present
                '    If Not String.IsNullOrEmpty(lprData.PlateNumber) Then
                '        TriggerBarrierOpen()
                '    End If

                '    Dim reply As String = $"ok:{lprData.SequenceId}"
                '    Dim replyData As Byte() = Encoding.UTF8.GetBytes(reply)
                '    stream.Write(replyData, 0, replyData.Length)
                '    Console.WriteLine($"Sent to Server: {reply}")
                'End If
                If lprData IsNot Nothing Then
                    ' Check if PlateNumber is present and validate against database
                    If Not String.IsNullOrEmpty(lprData.PlateNumber) Then
                        'Console.WriteLine($"LPR: Validating plate number: {lprData.PlateNumber}")

                        ' Check database for valid season pass
                        Dim isValidSeasonPlate As Boolean = mDBManager.IsValidSeasonPlate(lprData.PlateNumber)

                        If isValidSeasonPlate Then
                            Console.WriteLine($"LPR: Valid season pass found for plate: {lprData.PlateNumber} - Opening barrier")
                            TriggerBarrierOpen()

                            ' ADD THIS BLOCK AFTER LINE 4302:
                            ' Send transaction to SMC for running records
                            Dim trans As New TransactionMsg
                            'trans.message1 = lprData.PlateNumber
                            ' Get the IU associated with this license plate for proper SMC display
                            Dim associatedIU As String = mDBManager.GetIUForLicensePlate(lprData.PlateNumber)
                            trans.message1 = If(String.IsNullOrEmpty(associatedIU), lprData.PlateNumber, associatedIU)

                            trans.message2 = mTermInformation.TerminalNo
                            trans.message3 = Now.ToString("dd/MM/yyyy HH:mm:ss")
                            trans.message4 = 0
                            trans.TransactionType = ComCodes.TransactionTypeSelections.EntryIUSeason
                            trans.mMulticast = True
                            trans.mLog = True
                            'AddTranAndSend(lprData.PlateNumber, trans, ComCodes.TransactionTypeSelections.EntryIUSeasonRemoved)
                            AddTranAndSend(If(String.IsNullOrEmpty(associatedIU), lprData.PlateNumber, associatedIU), trans, ComCodes.TransactionTypeSelections.EntryIUSeasonRemoved)
                            ' ADD THESE LINES AFTER LINE 4345:
                            If HasEps() Then
                                ' Cast the Eps to BasicService to access ProcessRecvedEntryTransaction
                                CType(Eps, BasicService).ProcessRecvedEntryTransaction(trans, Enumeration.RunningStatus.ORIGINALSEASON, Enumeration.SEASONTOPTIONS.IU)
                            End If
                            'Console.WriteLine($"LPR: Opening barrier transaction sent to SMC for plate: {lprData.PlateNumber}")
                        Else
                            Console.WriteLine($"LPR: No valid season pass found for plate: {lprData.PlateNumber} - Access denied")
                            ' Barrier will not open for non-season pass holders
                        End If
                    Else
                        Console.WriteLine("LPR: No plate number detected")
                    End If

                    Dim reply As String = $"ok:{lprData.SequenceId}"
                    Dim replyData As Byte() = Encoding.UTF8.GetBytes(reply)
                    stream.Write(replyData, 0, replyData.Length)
                    Console.WriteLine($"Sent to Server: {reply}")
                End If
            Catch ex As Exception
                Console.WriteLine($"JSON Error: {ex.Message}")
            End Try
        End While
    End Sub


    Public Sub TriggerBarrierOpen()
        'Dim ip As String = "**********"
        'Dim username As String = "admin"
        'Dim password As String = "Mecomb2663$"
        Dim ip As String = mLprIP
        Dim username As String = mLprUsername
        Dim password As String = mLprPassword
        Dim url As String = $"http://{ip}/LAPI/V1.0/IO/OutputSwitches/ManualAlarm"
        'Dim jsonPayload As String = "{\"Num\":1, \"IDs\":[1], \"AlarmAction\":1}"
        Dim jsonPayload As String = "{""Num"":1, ""IDs"":[1], ""AlarmAction"":1}"

        Try
            Dim request As HttpWebRequest = CType(WebRequest.Create(url), HttpWebRequest)
            request.Credentials = New NetworkCredential(username, password)
            request.Method = "PUT"
            request.ContentType = "application/json"

            Dim data As Byte() = Encoding.UTF8.GetBytes(jsonPayload)
            request.ContentLength = data.Length

            Using stream As Stream = request.GetRequestStream()
                stream.Write(data, 0, data.Length)
            End Using

            Using response As HttpWebResponse = CType(request.GetResponse(), HttpWebResponse)
                Using reader As New StreamReader(response.GetResponseStream())
                    Dim responseBody As String = reader.ReadToEnd()
                    If response.StatusCode = HttpStatusCode.OK Then
                        'Console.WriteLine($"Barrier open signal sent at {DateTime.Now:yyyy-MM-dd HH:mm:ss}")
                    Else
                        Console.WriteLine($"Error: {response.StatusCode} - {responseBody}")
                    End If
                End Using
            End Using
        Catch ex As WebException
            Dim response As HttpWebResponse = TryCast(ex.Response, HttpWebResponse)
            If response IsNot Nothing Then
                Using reader As New StreamReader(response.GetResponseStream())
                    Console.WriteLine($"Error: {response.StatusCode} - {reader.ReadToEnd()}")
                End Using
            Else
                Console.WriteLine($"Failed: {ex.Message}")
            End If
        Catch ex As Exception
            Console.WriteLine($"Failed: {ex.Message}")
        End Try
    End Sub


    'Public Function IsValidSeasonPlate(ByVal plateNumber As String) As Boolean
    '    Dim con As SqlConnection = Nothing
    '    Try
    '        SyncLock connectionstr
    '            Dim thereader As SqlDataReader = executeCmd(connectionstr,
    '                "SELECT LicenceNo FROM IUSeasonInfo WHERE LicenceNo = @PlateNumber AND SeasonType = -1 AND GETDATE() BETWEEN ValidFrom AND ValidTo",
    '                New SqlParameter("PlateNumber", plateNumber), con)

    '            If thereader Is Nothing Then
    '                IsValidSeasonPlate = False
    '            Else
    '                If thereader.Read() = False Then
    '                    thereader.Close()
    '                    con.Close()
    '                    IsValidSeasonPlate = False
    '                Else
    '                    ' Found a matching record
    '                    thereader.Close()
    '                    con.Close()
    '                    IsValidSeasonPlate = True
    '                End If
    '            End If
    '        End SyncLock
    '    Catch ex As Exception
    '        'dblog.Log("Error in IsValidSeasonPlate for plate: " & plateNumber)
    '        'dblog.Log(ex.ToString())
    '        Console.WriteLine("Error in IsValidSeasonPlate for plate: " & plateNumber & " - " & ex.Message)

    '        ' Ensure connection is closed in case of error
    '        If con IsNot Nothing AndAlso con.State = ConnectionState.Open Then
    '            con.Close()
    '        End If

    '        IsValidSeasonPlate = False
    '    End Try
    'End Function



#End Region

End Class

Public Class LprData
    Public Property SequenceId As Integer
    Public Property PlateNumber As String
    Public Property Time As DateTime?
    Public Property GeneralPath As String
    Public Property SpecificPath As String
End Class